<template>
  <div class="sidebar-container h-screen">
    <div :class="['sidebar left-sidebar-bg-color text-white flex flex-col h-full', { collapsed }]">
      <!-- Header with Raleon logo -->
      <div class="sidebar-header flex items-center justify-between p-3">
        <div class="flex items-center">
          <span class="text-white font-medium text-lg">
            <span class="flex items-center">
              <img src="/client/images/RaleonDark.svg" height="24" class="mr-2" alt="Raleon Logo" />
            </span>
          </span>
        </div>
        <button @click="toggleCollapse" class="collapse-btn hover:bg-slate-600 transition-colors duration-200 rounded p-1 relative">
          <SidebarCollapseIcon class="default-icon" />
          <SidebarCollapseHoverIcon v-if="!collapsed" class="hover-icon absolute top-1 left-1" />
          <SidebarExpandHoverIcon v-if="collapsed" class="hover-icon absolute top-1 left-1" />
        </button>
      </div>

      <!-- Organization selector -->
      <div v-if="!collapsed" class="org-selector p-3 relative">
        <button @click="toggleOrgDropdown" class="w-full org-selector-button text-white rounded-md p-2 flex items-center justify-between">
          <div class="flex items-center">
            <div class="org-icon org-icon-bg text-white rounded-md w-10 h-8 flex-shrink-0 flex items-center justify-center mr-2">
              {{ currentOrg.initials }}
            </div>
            <span class="text-sm font-semibold truncate max-w-[140px]">{{ currentOrg.name }}</span>
          </div>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10H7Z" fill="#989898"></path>
          </svg>
        </button>

        <!-- Organization dropdown -->
        <div v-if="orgDropdownOpen" class="org-dropdown custom-scrollbar absolute left-3 right-3 top-full mt-1 rounded-md shadow-lg z-10">
          <div class="p-2">
            <!-- List of organizations -->
            <div
              v-for="org in organizations"
              :key="org.id"
              @click="switchOrganization(org)"
              :class="['flex items-center p-2 rounded-md mb-1 cursor-pointer',
                currentOrg.id === org.id ? 'org-dropdown-selected' : 'org-dropdown-item']">
              <div class="org-icon org-icon-bg text-white rounded-md w-10 h-8 flex-shrink-0 flex items-center justify-center mr-2">
                {{ org.initials }}
              </div>
              <span class="text-sm font-semibold truncate max-w-[140px]">{{ org.name }}</span>
            </div>

            <!-- Add new account option removed as requested -->
          </div>
        </div>
      </div>

      <!-- New Chat button -->
      <div class="p-3">
        <button @click="navigateToChat" class="w-full new-chat-button text-white rounded-md p-2 flex items-center justify-center">
          <span :class="collapsed ? 'mx-auto text-xl' : 'mr-1'">+</span>
          <span v-if="!collapsed">New Chat</span>
        </button>
      </div>

      <!-- Main navigation items -->
      <div class="flex-grow overflow-y-auto custom-scrollbar">
        <ul class="py-1">
          <li v-for="item in navigationItems" :key="item.path" class="px-2 py-1">
            <!-- Regular navigation items -->
            <router-link v-if="!item.expandable" :to="item.path"
              :class="['flex items-center py-2 px-4 rounded-md hover-effect text-sm',
                {'left-sidebar-highlight': $route.path.startsWith(item.routeMatch)},
                {'text-opacity-70': !$route.path.startsWith(item.routeMatch)}]">
              <div class="icon-container">
                <sidebar-icons :name="item.icon" class="mr-3 menu-icon" />
              </div>
              <span v-if="!collapsed" class="flex-grow menu-text">{{ item.label }}</span>
              <span v-if="item.badge"
                class="bg-purple-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {{ item.badge }}
              </span>
            </router-link>

            <!-- Expandable items (More Tools) -->
            <div v-else @click="toggleMoreTools"
              :class="['flex items-center py-2 px-4 rounded-md hover-effect cursor-pointer text-sm',
                {'left-sidebar-highlight': $route.path.startsWith(item.routeMatch)},
                {'text-opacity-70': !$route.path.startsWith(item.routeMatch)}]">
              <div class="icon-container">
                <sidebar-icons :name="item.icon" class="mr-3 menu-icon" />
              </div>
              <span v-if="!collapsed" class="flex-grow menu-text">{{ item.label }}</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="menu-dropdown-icon">
                <path d="M7 10L12 15L17 10H7Z" fill="#989898"></path>
              </svg>
            </div>

            <!-- Expanded More Tools items -->
            <div v-if="item.expandable && moreToolsExpanded" class="mt-1">
              <ul>
                <li v-for="subItem in moreToolsItems" :key="subItem.path" class="mb-1">
                  <!-- Regular submenu items -->
                  <router-link v-if="!subItem.expandable" :to="subItem.path"
                    :class="['flex items-center py-2 px-4 rounded-md hover-effect text-sm',
                      {'left-sidebar-highlight': $route.path.startsWith(subItem.routeMatch)},
                      {'text-opacity-70': !$route.path.startsWith(subItem.routeMatch)}]">
                    <div class="icon-container">
                      <sidebar-icons :name="subItem.icon" class="mr-2 menu-icon" />
                    </div>
                    <span v-if="!collapsed" class="menu-text">{{ subItem.label }}</span>
                  </router-link>

                  <!-- Expandable submenu items (Loyalty) -->
                  <div v-else @click="(event) => toggleLoyalty(event)"
                    :class="['flex items-center py-2 px-4 rounded-md hover-effect cursor-pointer text-sm',
                      {'left-sidebar-highlight': $route.path.startsWith(subItem.routeMatch)},
                      {'text-opacity-70': !$route.path.startsWith(subItem.routeMatch)}]">
                    <div class="icon-container">
                      <sidebar-icons :name="subItem.icon" class="mr-2 menu-icon" />
                    </div>
                    <span v-if="!collapsed" class="flex-grow menu-text">{{ subItem.label }}</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="menu-dropdown-icon">
                      <path d="M7 10L12 15L17 10H7Z" fill="#989898"></path>
                    </svg>
                  </div>

                  <!-- Expanded Loyalty submenu -->
                  <div v-if="subItem.expandable && loyaltyExpanded" class="pl-4 mt-1" @click.stop>
                    <ul>
                      <li v-for="loyaltyItem in loyaltyItems" :key="loyaltyItem.path" class="mb-1">
                        <router-link :to="loyaltyItem.path"
                          :class="['flex items-center py-1 px-4 rounded-md hover-effect text-xs',
                            {'left-sidebar-highlight': loyaltyItem.name === 'points' ? $route.path === '/loyalty/program' : $route.path.startsWith(loyaltyItem.routeMatch)},
                            {'text-opacity-70': loyaltyItem.name === 'points' ? $route.path !== '/loyalty/program' : !$route.path.startsWith(loyaltyItem.routeMatch)}]">
                          <span v-if="!collapsed" class="menu-text">{{ loyaltyItem.label }}</span>
                        </router-link>
                      </li>
                    </ul>
                  </div>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </div>

      <!-- Bottom section with settings, help, etc. -->
      <div class="mt-auto">
        <!-- Bottom menu dropdown -->
        <div v-if="bottomMenuExpanded" class="mb-2 mx-3">
          <div class="bottom-menu-container rounded-md py-2">
            <ul>
              <li v-for="item in bottomItems" :key="item.name" class="px-2">
                <template v-if="!item.condition || item.condition()">
                  <!-- Button items -->
                  <button v-if="item.isButton" @click="item.action"
                    class="w-full flex items-center py-2 px-4 text-white text-sm hover-menu-item">
                    <div class="icon-container">
                      <sidebar-icons :name="item.icon" />
                    </div>
                    <span v-if="!collapsed">{{ item.label }}</span>
                  </button>

                  <!-- Link items -->
                  <component v-else :is="item.external ? 'a' : 'router-link'"
                    :to="!item.external && item.path"
                    :href="item.external && item.path"
                    :target="item.external ? '_blank' : null"
                    :rel="item.external ? 'noopener noreferrer' : null"
                    :class="['flex items-center py-2 px-4 text-sm hover-menu-item',
                      {'menu-item-active': !item.external && $route.path.startsWith(item.routeMatch)}]">
                    <div class="icon-container">
                      <sidebar-icons :name="item.icon" />
                    </div>
                    <span v-if="!collapsed">{{ item.label }}</span>
                  </component>
                </template>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Message Quota Section - Only show for trial users -->
      <div v-if="shouldShowMessageQuota" class="px-3 pb-2">
        <MessageQuotaIndicator
          class="quota-indicator-bottom"
          @click="showQuotaModal = true"
        />
      </div>

      <!-- Team/User info at bottom -->
      <div class="p-3 border-t border-slate-700">
        <div class="flex items-center justify-between cursor-pointer" @click="toggleBottomMenu">
          <div class="flex items-center">
            <div class="team-icon bg-purple-600 text-white rounded-full w-10 h-8 flex-shrink-0 flex items-center justify-center mr-2">
              {{ getUserInitials }}
            </div>
            <div v-if="!collapsed" class="flex flex-col overflow-hidden">
              <span class="text-sm font-semibold truncate max-w-[140px]">{{ userName }}</span>
              <span class="text-xs text-purple-400">{{ planStatus }}</span>
            </div>
          </div>
          <button class="text-white">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 15L12 10L17 15H7Z" fill="#989898"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Organization Switcher Modal -->
    <OrganizationSwitcherModal :show="orgSwitcherOpen" :userOrgs="userOrgs" @close="orgSwitcherOpen = false" />
    <MessageQuotaModal :show="showQuotaModal" @close="showQuotaModal = false" />

    <!-- Trial Ended Modal - Blocking -->
    <TrialEndedModal :show="shouldShowTrialEndedModal" />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import * as Utils from '../../client-old/utils/Utils';
import SidebarIcons from './SidebarIcons.ts.vue';
import OrganizationSwitcherModal from './OrganizationSwitcherModal.vue';
import MessageQuotaIndicator from './MessageQuotaIndicator.ts.vue';
import MessageQuotaModal from './MessageQuotaModal.ts.vue';
import TrialEndedModal from './TrialEndedModal.ts.vue';
import SidebarCollapseIcon from './icons/SidebarCollapseIcon.vue';
import SidebarCollapseHoverIcon from './icons/SidebarCollapseHoverIcon.vue';
import SidebarExpandHoverIcon from './icons/SidebarExpandHoverIcon.vue';
import { freeTrialInfo, isFeatureAvailable } from '../services/features.js';

const URL_DOMAIN = Utils.URL_DOMAIN;
const PLAN_IDS = Utils.PLAN_IDS;

export default {
  name: 'Sidebar',
  components: {
    SidebarIcons,
    OrganizationSwitcherModal,
    MessageQuotaIndicator,
    MessageQuotaModal,
    TrialEndedModal,
    SidebarCollapseIcon,
    SidebarCollapseHoverIcon,
    SidebarExpandHoverIcon
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const orgSwitcherOpen = ref(false);
    const orgDropdownOpen = ref(false);
    const collapsed = ref(false);
    const moreToolsExpanded = ref(false);
    const loyaltyExpanded = ref(false);
    const bottomMenuExpanded = ref(false);
    const showQuotaModal = ref(false);
    const userOrgs = ref([]);
    const freeTrialData = ref({});
    const freeTrialDataLoaded = ref(false);
    const runOnboarding = ref(null);
    const dev_doc_url = ref('https://docs.raleon.io');

    // More Tools submenu items (removed integrations)
    const moreToolsItems = ref([
      { name: 'giftWithPurchase', path: '/promotions/gift-with-purchase', routeMatch: '/promotions/gift-with-purchase', icon: 'gift', label: 'Gift With Purchase' }
    ]);

    // Loyalty submenu items
    const loyaltyItems = ref([
      { name: 'getStarted', path: '/loyalty/quickstart', routeMatch: '/loyalty/quickstart', label: 'Get Started' },
      { name: 'customers', path: '/loyalty/customers', routeMatch: '/loyalty/customers', label: 'Customers' },
      { name: 'points', path: '/loyalty/program', routeMatch: '/loyalty/program', label: 'Points' },
      { name: 'vip', path: '/loyalty/program/vip', routeMatch: '/loyalty/program/vip', label: 'VIP' },
      { name: 'referrals', path: '/loyalty/program/referrals', routeMatch: '/loyalty/program/referrals', label: 'Referrals' },
      { name: 'branding', path: '/loyalty/onsite', routeMatch: '/loyalty/onsite', label: 'Branding' },
      { name: 'campaigns', path: '/loyalty/campaigns', routeMatch: '/loyalty/campaigns', label: 'Campaigns' },
      { name: 'analytics', path: '/loyalty/analytics', routeMatch: '/loyalty/analytics', label: 'Analytics' },
      { name: 'communication', path: '/loyalty/communication', routeMatch: '/loyalty/communication', label: 'Communication' }
    ]);

    // Base navigation items - computed to filter based on plan
    const baseNavigationItems = [
      { name: 'chat', path: '/chats', routeMatch: '/chat', icon: 'chat', label: 'Chats' },
      { name: 'knowledge', path: '/ai-strategist/knowledge', routeMatch: '/ai-strategist/knowledge', icon: 'knowledge', label: 'Knowledge' },
      { name: 'commandCenter', path: '/ai-strategist/planning', routeMatch: '/ai-strategist/planning', icon: 'command-center', label: 'Command Center' },
      { name: 'calendar', path: '/ai-strategist/tasks', routeMatch: '/ai-strategist/tasks', icon: 'calendar', label: 'Calendar' },
      { name: 'segments', path: '/ai-segments/overview', routeMatch: '/ai-segments', icon: 'ai-segments', label: 'Segments' },
      { name: 'analytics', path: '/ai-strategist/analytics', routeMatch: '/ai-strategist/analytics', icon: 'analytics', label: 'Analytics' },
      { name: 'integrations', path: '/integrations', routeMatch: '/integrations', icon: 'integrations', label: 'Integrations' },
      { name: 'moreTools', routeMatch: '/more-tools', icon: 'more', label: 'More Tools', expandable: true }
    ];

    // Navigation items filtered based on plan - hide More Tools for non-legacy/non-Strategist plans
    const navigationItems = computed(() => {
      return baseNavigationItems.filter(item => {
        // Hide More Tools section for users not on legacy (1-12) or Strategist plans (13-17)
        if (item.name === 'moreTools' && !shouldShowMoreTools.value) {
          return false;
        }
        return true;
      });
    });

    // Bottom items (settings, help, billing, sign out)
    const bottomItems = ref([
      { name: 'settings', path: '/settings', routeMatch: '/settings', icon: 'settings', label: 'Settings' },
      { name: 'brands', path: '/agency', routeMatch: '/agency', icon: 'customer', label: 'Manage Brands', condition: () => shouldShowManageBrands.value },
      {
        name: 'messages',
        icon: 'chat',
        label: 'Messages',
        isButton: true,
        action: () => {
          showQuotaModal.value = true;
        }
      },
      { name: 'help', path: 'https://docs.raleon.io', icon: 'help', label: 'Help', external: true },
      { name: 'billing', path: '/loyalty/settings/plans', routeMatch: '/loyalty/settings/plans', icon: 'billing', label: 'Billing' },
      {
        name: 'signOut',
        icon: 'sign-out',
        label: 'Sign Out',
        isButton: true,
        action: () => {
          // Sign out logic
          localStorage.removeItem('token');
          localStorage.removeItem('userInfo');
          router.push('/signin');
        }
      }
    ]);

    // Organization data
    const organizations = computed(() => {
      return userOrgs.value.map(org => ({
        id: org.id,
        name: org.name,
        initials: getOrgInitials(org.name)
      }));
    });

    const currentOrgId = computed(() => {
      if (typeof window !== 'undefined') {
        return localStorage.getItem('userOrgId');
      }
      return null;
    });

    const currentOrg = computed(() => {
      if (organizations.value.length === 0) return { id: null, name: 'Loading...', initials: '...' };
      const org = organizations.value.find(org => org.id.toString() === currentOrgId.value);
      return org || organizations.value[0]; // Default to first org if current not found
    });

    const currentOrgName = computed(() => {
      return currentOrg.value?.name || 'N/A';
    });

    const userName = computed(() => {
      if (typeof window !== 'undefined') {
        const firstName = localStorage.getItem('firstName') || '';
        const lastName = localStorage.getItem('lastName') || '';
        return `${firstName} ${lastName}`.trim() || 'User';
      }
      return 'User';
    });

    // Determine plan status based on freeTrialData - only show when loaded
    const planStatus = computed(() => {
      // Don't show any status until trial data is loaded to prevent flicker
      if (!freeTrialDataLoaded.value) {
        return '';
      }
      
      if (freeTrialData.value?.hasPaidPlan) {
        return 'Active Plan';
      } else if (freeTrialData.value?.inEffect) {
        return 'Trial Active';
      } else {
        return 'Trial Ended';
      }
    });

    // Check if trial ended modal should be shown
    const shouldShowTrialEndedModal = computed(() => {
      // Don't show until trial data is loaded
      if (!freeTrialDataLoaded.value) {
        return false;
      }

      // Don't show on billing/plans page
      if (route.path === '/loyalty/settings/plans') {
        return false;
      }

      // Show if trial has ended (no paid plan and trial not in effect)
      return freeTrialData.value &&
             !freeTrialData.value.hasPaidPlan &&
             !freeTrialData.value.inEffect;
    });

    // Check if message quota counter should be shown (only for trial users)
    const shouldShowMessageQuota = computed(() => {
      // Don't show until trial data is loaded
      if (!freeTrialDataLoaded.value) {
        return false;
      }

      // Only show for users who are currently on trial (not paid plan)
      return freeTrialData.value &&
             !freeTrialData.value.hasPaidPlan &&
             freeTrialData.value.inEffect;
    });

    // Check if Shopify is connected
    const shopifyConnected = ref(true);

    const checkShopifyConnection = async () => {
      try {
        const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          }
        });
        if (!response.ok) {
          throw new Error('Failed to check Shopify connection');
        }
        const data = await response.json();
        shopifyConnected.value = data.connected;
      } catch (error) {
        console.error('Error checking Shopify connection:', error);
        shopifyConnected.value = false;
      }
    };

	const isLoyaltyAvailable = computed(() => {
		return isFeatureAvailable('points') && shopifyConnected.value;
	});

	// Check if user should see More Tools (Legacy customers only, NOT Strategist plans)
	const shouldShowMoreTools = computed(() => {
		try {
			const featureStates = JSON.parse(localStorage.getItem('featureStates') || '{}');
			const planId = featureStates?.plan?.id;

			// Show More Tools ONLY for:
			// - Legacy customers (plans 1-12)
			// Hide More Tools for:
			// - Strategist plans (plans 13-17) - they don't need legacy loyalty features
			// - Any other plans
			return planId && (planId >= PLAN_IDS.LEGACY_START && planId <= PLAN_IDS.LEGACY_END);
		} catch (error) {
			console.error('Error checking plan for More Tools visibility:', error);
			return false;
		}
	});

	// Check if user should see Manage Brands (only for Agency Platform)
	const shouldShowManageBrands = computed(() => {
		try {
			const featureStates = JSON.parse(localStorage.getItem('featureStates') || '{}');
			const planId = featureStates?.plan?.id;

			// Show Manage Brands ONLY for:
			// - Agency Platform
			return planId === PLAN_IDS.AGENCY_PLATFORM;
		} catch (error) {
			console.error('Error checking plan for Manage Brands visibility:', error);
			return false;
		}
	});

    // Get initials from a name (e.g., "John Doe" -> "JD")
    const getOrgInitials = (name) => {
      if (!name) return 'NA';
      return name
        .split(' ')
        .map(word => word[0])
        .join('')
        .substring(0, 2)
        .toUpperCase();
    };

    // Get user initials from localStorage
    const getUserInitials = computed(() => {
      if (typeof window !== 'undefined') {
        const firstName = localStorage.getItem('firstName') || '';
        const lastName = localStorage.getItem('lastName') || '';
        if (firstName || lastName) {
          return (firstName.charAt(0) + (lastName.charAt(0) || '')).toUpperCase();
        }
      }
      return 'U';
    });

    const switchOrganization = async (org) => {
      if (org.id.toString() === currentOrgId.value) {
        orgDropdownOpen.value = false;
        return; // Already on this org
      }

      try {
        const loginRequest = await fetch(`${URL_DOMAIN}/users/login/${org.id}`, {
          method: 'POST',
          credentials: 'omit',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          }
        });

        if (!loginRequest.ok) {
          console.error(`Failed to switch organization: ${loginRequest.status} ${loginRequest.statusText}`);
          orgDropdownOpen.value = false;
          return;
        }

        const loginResult = await loginRequest.json();
        if (loginResult.token) {
          localStorage.setItem('token', loginResult.token);
          localStorage.setItem('userOrgId', org.id.toString());
          localStorage.removeItem('metricsCache');
          window.location.assign('/ai-strategist/planning'); // Force reload
        } else {
          console.error('No token received after switching organization.');
          orgDropdownOpen.value = false;
        }
      } catch (error) {
        console.error('Error during organization switch:', error);
        orgDropdownOpen.value = false;
      }
    };

    // addNewAccount function removed as the option was removed from UI

    const toggleOrgDropdown = () => {
      orgDropdownOpen.value = !orgDropdownOpen.value;
    };

    const toggleCollapse = () => {
      collapsed.value = !collapsed.value;
      // Close dropdowns when collapsing
      if (collapsed.value) {
        orgDropdownOpen.value = false;
        moreToolsExpanded.value = false;
        loyaltyExpanded.value = false;
        bottomMenuExpanded.value = false;
      }
    };

    const toggleMoreTools = () => {
      moreToolsExpanded.value = !moreToolsExpanded.value;
      // Only close the bottom menu if it's open
      if (moreToolsExpanded.value && bottomMenuExpanded.value) {
        bottomMenuExpanded.value = false;
      }
      // If More Tools is being closed, also close the Loyalty submenu
      if (!moreToolsExpanded.value) {
        loyaltyExpanded.value = false;
      }
    };

    const toggleLoyalty = (event) => {
      // Stop event propagation to prevent closing the More Tools menu
      event.stopPropagation();
      loyaltyExpanded.value = !loyaltyExpanded.value;
      // Only close the bottom menu if it's open
      if (loyaltyExpanded.value && bottomMenuExpanded.value) {
        bottomMenuExpanded.value = false;
      }
    };

    const toggleBottomMenu = () => {
      // If the sidebar is collapsed, expand it before showing the menu
      if (collapsed.value) {
        collapsed.value = false;
        // ensure other dropdowns are closed when expanding
        orgDropdownOpen.value = false;
        moreToolsExpanded.value = false;
        loyaltyExpanded.value = false;
      }

      bottomMenuExpanded.value = !bottomMenuExpanded.value;
      // Close other menus if they're open
      if (bottomMenuExpanded.value) {
        if (moreToolsExpanded.value) {
          moreToolsExpanded.value = false;
          loyaltyExpanded.value = false;
        }
      }
    };

    const navigateToChat = () => {
      // Check if we're already on the chat page
      if (route.path.startsWith('/chat')) {
        // Force a new chat by navigating to a different route first, then to /chat
        router.push('/chats').then(() => {
          // Use nextTick to ensure the route change has been processed
          setTimeout(() => {
            router.push('/chat');
          }, 10);
        });
      } else {
        // Normal navigation if we're not already on the chat page
        router.push('/chat');
      }
    };

    // Close dropdown when clicking outside
    onMounted(() => {
      document.addEventListener('click', (event) => {
        const dropdown = document.querySelector('.org-selector');
        if (dropdown && !dropdown.contains(event.target)) {
          orgDropdownOpen.value = false;
        }
      });
    });

    // This is now handled by the new currentOrgName computed property above

    // Fetch user organizations
    const fetchUserOrgs = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await fetch(`${URL_DOMAIN}/user/orgs`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          console.error('Failed to fetch user organizations');
          return;
        }

        const data = await response.json();
        userOrgs.value = data.orgs || [];
      } catch (error) {
        console.error('Error fetching user organizations:', error);
      }
    };

    onMounted(async () => {
      // Fetch user organizations
      await fetchUserOrgs();

      // Fetch Free Trial Info
      freeTrialData.value = await freeTrialInfo();
      freeTrialDataLoaded.value = true;
      localStorage.setItem('freeTrialData', JSON.stringify(freeTrialData.value));

      // Check Shopify connection
      await checkShopifyConnection();

      // Add loyalty item to moreToolsItems based on availability
      const loyaltyItem = {
        name: 'loyalty',
        path: '/loyalty/program',
        routeMatch: '/loyalty',
        icon: 'free-product-reward',
        label: 'Loyalty',
        expandable: isLoyaltyAvailable.value
      };

      // Add the loyalty item to moreToolsItems
      moreToolsItems.value.unshift(loyaltyItem);

      // Fetch Organizations
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('Authentication token not found for fetching orgs.');
          return;
        }

        const response = await fetch(`${URL_DOMAIN}/user/orgs`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          }
        });
        if (!response.ok) {
          console.error(`Failed to fetch organizations: ${response.status} ${response.statusText}`);
          return;
        }
        userOrgs.value = await response.json();
      } catch (error) {
        console.error('Failed to fetch organizations:', error);
      }

      // Fetch Docs URL
      try {
        const docLoginResponse = await fetch(`${URL_DOMAIN}/users/doc-login`, {
          method: 'GET',
          credentials: 'omit',
          mode: 'cors',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json'
          }
        });
        const docData = await docLoginResponse.json();
        if (docData.docs_url) dev_doc_url.value = docData.docs_url;
      } catch (err) {
        console.error("Error fetching docs URL:", err);
      }

      // Check Onboarding Status
      if (runOnboarding.value == null) {
        try {
          const onboardingResponse = await fetch(`${URL_DOMAIN}/onboarding-tasks/states`, {
            method: 'GET',
            credentials: 'omit',
            mode: 'cors',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${localStorage.getItem('token')}`,
            },
          });
          const tasks = await onboardingResponse.json();
          runOnboarding.value = tasks
            .sort((a, b) => a.priority - b.priority)
            .filter(x => !x.type.includes('nsights'))
            .some(x => !x.state || !x.state?.state?.includes('erified'));
          localStorage.setItem('runOnboarding', runOnboarding.value);
        } catch (error) {
          console.error("Error fetching onboarding status:", error);
        }
      }
    });

    // Cleanup resize listener if needed
    onUnmounted(() => {
      // Any cleanup code here
    });

    return {
      isActiveSettings: computed(() => route.path.startsWith('/settings')),
      userInfo: computed(() => JSON.parse(localStorage.getItem('userInfo') || '{}')),
      currentRoute: router.currentRoute,
      orgSwitcherOpen,
      orgDropdownOpen,
      toggleOrgDropdown,
      collapsed,
      toggleCollapse,
      organizations,
      currentOrg,
      currentOrgId,
      currentOrgName,
      userName,
      getUserInitials,
      planStatus,
      shouldShowTrialEndedModal,
      shouldShowMessageQuota,
      switchOrganization,
      userOrgs,
      fetchUserOrgs,
      freeTrialData,
      runOnboarding,
      dev_doc_url,
      moreToolsExpanded,
      toggleMoreTools,
      loyaltyExpanded,
      toggleLoyalty,
      bottomMenuExpanded,
      toggleBottomMenu,
      showQuotaModal,
      navigateToChat,
      navigationItems,
      bottomItems,
      loyaltyItems,
      moreToolsItems,
      shouldShowMoreTools,
      shouldShowManageBrands
    };
  },
};
</script>

<style scoped>
.sidebar-container {
  position: relative;
}

.sidebar {
  width: 250px;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar.collapsed .menu-text,
.sidebar.collapsed .org-selector,
.sidebar.collapsed .bottom-menu-container span,
.sidebar.collapsed .sidebar-header span.text-white.font-medium {
  display: none;
}

.sidebar.collapsed .icon-container {
  margin-right: 0;
}

/* Icon container styling */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  min-width: 26px;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1d1644;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #332567;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #6e3ff2;
}

/* Firefox scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #332567 #1d1644;
}

.left-sidebar-bg-color {
  background-color: #1d1644;
}

.left-sidebar-highlight {
  background-color: #332567;
  border-radius: 8px;
}

.hover-effect {
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.hover-effect:hover {
  background-color: #5C5B9E;
}

.org-icon, .team-icon {
  font-weight: bold;
}

.org-icon-bg {
  background-color: #6c63ff;
}

.org-selector-button {
  background-color: #2c2257;
  transition: background-color 0.2s ease;
}

.org-selector-button:hover {
  background-color: #332567;
}

.org-dropdown {
  background-color: #2c2257;
  width: calc(100% - 1.5rem); /* Full width minus padding */
  max-height: 300px;
  overflow-y: auto;
}

.org-dropdown-item {
  transition: background-color 0.2s ease;
}

.org-dropdown-item:hover {
  background-color: #332567;
}

.bottom-menu-container {
  background-color: #2c2257;
  transition: background-color 0.2s ease;
}

.hover-menu-item {
  border-radius: 4px;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.hover-menu-item:hover {
  background-color: #332567;
}

.menu-item-active {
  background-color: #332567;
}

.org-dropdown-selected {
  background-color: #332567;
}

.new-chat-button {
  background-color: #6e3ff2;
}

.new-chat-button:hover {
  background-color: #5a33d8;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

/* Ensure router-links and anchor tags have pointer cursor */
.sidebar a, .sidebar router-link {
  cursor: pointer;
}

/* Message quota indicator styling in bottom position */
.quota-indicator-bottom {
  width: 100%;
  border-radius: 8px;
  background: #2c2257;
  border: 1px solid #332567;
  transition: all 0.2s ease;
  cursor: pointer;
}

.quota-indicator-bottom:hover {
  background: #332567;
  border-color: #6e3ff2;
}

/* Hide quota indicator when sidebar is collapsed */
.sidebar.collapsed .quota-indicator-bottom {
  display: none;
}

/* Collapse button hover effects */
.collapse-btn {
  position: relative;
}

.collapse-btn .hover-icon {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.collapse-btn:hover .default-icon {
  opacity: 0;
}

.collapse-btn:hover .hover-icon {
  opacity: 1;
}
</style>
