<template>
	<!-- Blurred Background Overlay -->
        <div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto overflow-x-hidden p-4" style="background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(8px);">
		<!-- Modal Container -->
                <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 relative overflow-y-auto overflow-x-hidden max-h-full">

			<!-- Header -->
			<div class="px-8 py-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
				<h2 class="text-2xl font-bold mb-2">Complete Your Setup</h2>
				<p class="text-indigo-100">Help us personalize your Raleon experience</p>
			</div>

			<!-- Progress indicator -->
			<div class="px-8 py-4 bg-gray-50">
				<div class="flex items-center">
					<div v-for="step in 3" :key="step" class="flex items-center">
						<div :class="[
							'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300',
							currentStep >= step
								? 'bg-indigo-500 text-white'
								: 'bg-gray-200 text-gray-600'
						]">
							{{ step }}
						</div>
						<div v-if="step < 3" :class="[
							'w-16 h-0.5 mx-4 transition-all duration-300',
							currentStep > step ? 'bg-indigo-500' : 'bg-gray-200'
						]"></div>
					</div>
				</div>
			</div>

			<!-- Content -->
			<div class="px-8 py-6" style="min-height: 400px;">
				<!-- Step 1: User Type Selection -->
				<div v-if="currentStep === 1">
					<h3 class="text-xl font-semibold mb-2">Who are you? 🤔</h3>
					<p class="text-gray-600 mb-6">Help us customize your experience</p>

					<div class="space-y-4">
						<div
							v-for="type in userTypes"
							:key="type.value"
							@click="type.disabled ? null : selectUserType(type.value)"
							:class="[
								'p-6 border-2 rounded-lg transition-all duration-200',
								type.disabled
									? 'cursor-not-allowed border-gray-200 bg-gray-50 opacity-60'
									: 'cursor-pointer hover:border-indigo-300',
								formData.orgType === type.value
									? 'border-indigo-500 bg-indigo-50'
									: 'border-gray-200 bg-white'
							]"
						>
							<div class="flex items-center">
								<div :class="[
									'w-4 h-4 rounded-full border-2 mr-4 flex-shrink-0',
									formData.orgType === type.value
										? 'border-indigo-500 bg-indigo-500'
										: 'border-gray-300'
								]">
									<div v-if="formData.orgType === type.value" class="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
								</div>
								<div class="flex-1">
									<h4 :class="[
										'text-lg font-medium',
										type.disabled ? 'text-gray-400' : 'text-gray-900'
									]">{{ type.label }}</h4>
									<p :class="[
										'text-sm',
										type.disabled ? 'text-gray-400' : 'text-gray-500'
									]">{{ type.description }}</p>
									<div v-if="type.disabled && type.value === 'agency'" class="mt-2">
										<p class="text-xs text-blue-600">
											Agency onboarding is coming soon! You can onboard a brand and convert to an agency later.
										</p>
										<a
											href="https://raleon.io/agency-partners"
											target="_blank"
											class="text-xs text-blue-600 underline hover:text-blue-700"
										>
											Learn more about agency partnerships
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Step 2: Personal Details & Goals -->
				<div v-if="currentStep === 2">
					<h3 class="text-xl font-semibold mb-2">Tell us about yourself 👋</h3>
					<p class="text-gray-600 mb-6">Help us personalize your Raleon experience</p>

					<div class="space-y-6">
						<!-- Name Input -->
						<div>
							<label class="block text-sm font-medium mb-1" for="firstName">
								Your Name
							</label>
							<input
								id="firstName"
								class="form-input w-full"
								type="text"
								v-model="formData.firstName"
								required
								placeholder="Your full name"
							/>
						</div>

						<!-- Goals Selection -->
						<div>
							<label class="block text-sm font-medium mb-3">
								<span v-if="formData.orgType === 'brand'">What are your overall goals?</span>
								<span v-else-if="formData.orgType === 'agency'">What problems are you trying to solve?</span>
								<span v-else>What are you looking to accomplish?</span>
								<span class="text-xs text-gray-500 ml-1">(select all that apply)</span>
							</label>
							<div class="flex flex-wrap gap-2">
								<button
									v-for="goal in currentGoalOptions"
									:key="goal.value"
									type="button"
									@click="toggleGoal(goal.value)"
									:class="[
										'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 border',
										formData.goals.includes(goal.value)
											? 'bg-indigo-500 text-white border-indigo-500 hover:bg-indigo-600'
											: 'bg-white text-gray-700 border-gray-300 hover:border-indigo-300 hover:text-indigo-600'
									]"
									:title="goal.description"
								>
									{{ goal.label }}
								</button>
							</div>
							<p class="text-xs text-gray-500 mt-2">💡 Hover over any option to see more details</p>
						</div>
					</div>
				</div>

				<!-- Step 3: Organization Details -->
				<div v-if="currentStep === 3">
					<h3 class="text-xl font-semibold mb-2">{{ getStep2Title() }} 📋</h3>
					<p class="text-gray-600 mb-6">{{ getStep2Description() }}</p>

					<form @submit.prevent="completeOnboarding">
						<div class="space-y-4">
							<div v-if="formData.orgType !== 'individual'">
								<label class="block text-sm font-medium mb-1" for="orgName">
									{{ formData.orgType === 'agency' ? 'Agency Name' : 'Brand Name' }}
								</label>
								<input
									id="orgName"
									class="form-input w-full"
									type="text"
									v-model="formData.organizationName"
									required
									:placeholder="formData.orgType === 'agency' ? 'Your agency name' : 'Your brand name'"
								/>
							</div>

							<div v-if="formData.orgType === 'brand' || formData.orgType === 'individual'">
								<label class="block text-sm font-medium mb-1" for="domain">
									Website URL
									<span class="text-xs text-gray-500 ml-1">(we'll verify this)</span>
								</label>
								<div class="relative">
									<input
										id="domain"
										class="form-input w-full pr-10"
										type="text"
										v-model="formData.externalDomain"
										required
										placeholder="example.com"
										@blur="validateUrl"
									/>
									<div v-if="urlValidating" class="absolute right-3 top-1/2 transform -translate-y-1/2">
										<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
									</div>
									<div v-else-if="urlValid === true" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
										✓
									</div>
									<div v-else-if="urlValid === false" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500">
										✗
									</div>
								</div>
                                                               <div v-if="urlValid === false" class="text-red-500 text-xs mt-1">
                                                                        We couldn't verify this URL. Please check and try again.
                                                               </div>

                                                               <div>
                                                                       <label class="block text-sm font-medium mb-1" for="revenue">What was your brand's revenue in the last 12 months?</label>
                                                                       <select id="revenue" class="form-input w-full" v-model.number="formData.revenue">
                                                                               <option disabled :value="null">Select revenue range</option>
                                                                               <option v-for="rev in revenues" :key="rev.value" :value="rev.value">{{ rev.label }}</option>
                                                                       </select>
                                                               </div>

                                                                <!-- AI Context Information -->
								<div class="mt-3 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg">
									<div class="flex items-start space-x-3">
										<div class="flex-shrink-0 mt-0.5">
											<svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
											</svg>
										</div>
										<div class="flex-1">
											<h4 class="text-sm font-medium text-purple-900 mb-1">🧠 How this powers your AI experience</h4>
											<p class="text-xs text-purple-700 leading-relaxed">
												We analyze your website to understand your brand voice, products, and messaging style. This helps our AI generate emails, segments, and campaigns that sound authentically like your brand from day one.
											</p>
											<div class="mt-2 space-y-1">
												<div class="flex items-center text-xs text-purple-600">
													<svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
														<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
													</svg>
													Learn your brand tone and voice
												</div>
												<div class="flex items-center text-xs text-purple-600">
													<svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
														<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
													</svg>
													Understand your products and services
												</div>
												<div class="flex items-center text-xs text-purple-600">
													<svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
														<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
													</svg>
													Generate personalized content suggestions
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

						</div>

						<div v-if="formData.orgType === 'agency'" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
							<p class="text-sm text-blue-700">
								🎯 As an agency, you can add and manage multiple brands after account creation.
							</p>
						</div>
					</form>
				</div>

				<!-- Error Message -->
				<div v-if="error" class="mt-4 flex items-center p-4 text-gray-500 bg-red-100 border border-red-400 rounded-lg shadow">
					<div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-red-500 bg-red-100 rounded-lg text-red-700">
						<svg aria-hidden="true" class="w-5 h-5" fill="#f56565" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
						</svg>
					</div>
					<div class="ml-3 text-sm font-normal">{{ error }}</div>
				</div>
			</div>

			<!-- Footer -->
			<div class="px-8 py-6 bg-gray-50 flex justify-between">
				<button
					v-if="currentStep > 1"
					@click="goToPreviousStep"
					class="btn bg-gray-300 hover:bg-gray-400 text-gray-700"
				>
					Back
				</button>
				<div v-else></div>
				<button
					@click="handleNextStep"
					class="btn bg-indigo-500 hover:bg-indigo-600 text-white"
					:disabled="!canProceed"
				>
					<span v-if="loading">Processing...</span>
					<span v-else-if="currentStep === 1">Continue</span>
					<span v-else-if="currentStep === 2">Continue</span>
					<span v-else>Complete Setup</span>
				</button>
			</div>
		</div>
	</div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import { REVENUE_RANGES } from '../../client-old/utils/Utils';
import * as OrganizationSettings from '../services/organization-settings.js';
import * as UserService from '../services/user.js';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'OnboardingModal',
	emits: ['complete'],
	data() {
		// Load any existing form data from signup
		const savedFormData = localStorage.getItem('signup_form_data');
		const parsedData = savedFormData ? JSON.parse(savedFormData) : {};

		return {
			currentStep: 1,
                        formData: {
                                orgType: '',
                                organizationName: '',
                                externalDomain: parsedData.externalDomain || '',
                                firstName: '',
                                goals: [],
                                revenue: null
                        },
			loading: false,
			error: null,
			urlValidating: false,
			urlValid: null,
			userTypes: [
				{
					value: 'brand',
					label: 'Brand',
					description: 'I represent a single brand or company'
				},
				{
					value: 'agency',
					label: 'Agency',
					description: 'I work with multiple brands and clients - you can add and manage multiple brands',
					disabled: true
				}
			],
			goalOptions: {
				brand: [
					{ value: 'faster_emails', label: 'Faster email creation', description: 'Speed up your email marketing workflow' },
					{ value: 'more_emails', label: 'Send more emails', description: 'Increase email frequency and volume' },
					{ value: 'better_targeting', label: 'Better targeting', description: 'Improve audience segmentation and personalization' },
					{ value: 'email_planning', label: 'Email planning', description: 'Strategic campaign planning and scheduling' },
					{ value: 'data_analytics', label: 'Data & analytics', description: 'Better insights and performance tracking' },
					{ value: 'automation', label: 'Marketing automation', description: 'Automate repetitive marketing tasks' },
					{ value: 'conversion_optimization', label: 'Conversion optimization', description: 'Improve email conversion rates' },
					{ value: 'brand_consistency', label: 'Brand consistency', description: 'Maintain consistent brand messaging' }
				],
				agency: [
					{ value: 'efficiency', label: 'More efficiency', description: 'Streamline workflows and save time' },
					{ value: 'better_targeting', label: 'Better targeting', description: 'Improve client campaign performance' },
					{ value: 'better_roi', label: 'Better ROI', description: 'Deliver higher returns for clients' },
					{ value: 'client_reporting', label: 'Client reporting', description: 'Enhanced reporting and analytics for clients' },
					{ value: 'scale_operations', label: 'Scale operations', description: 'Handle more clients efficiently' },
					{ value: 'competitive_advantage', label: 'Competitive advantage', description: 'Stand out from other agencies' },
					{ value: 'client_retention', label: 'Client retention', description: 'Keep clients happy and engaged' },
					{ value: 'faster_delivery', label: 'Faster delivery', description: 'Speed up campaign creation and launch' }
				],
				individual: [
					{ value: 'grow_business', label: 'Grow my business', description: 'Expand reach and increase sales' },
					{ value: 'save_time', label: 'Save time', description: 'Automate and streamline marketing tasks' },
					{ value: 'better_targeting', label: 'Better targeting', description: 'Reach the right audience effectively' },
					{ value: 'professional_emails', label: 'Professional emails', description: 'Create polished, professional campaigns' },
					{ value: 'understand_analytics', label: 'Understand analytics', description: 'Make data-driven marketing decisions' },
					{ value: 'compete_better', label: 'Compete with bigger brands', description: 'Level the playing field with enterprise tools' },
					{ value: 'build_audience', label: 'Build my audience', description: 'Grow and engage your customer base' },
                                { value: 'increase_sales', label: 'Increase sales', description: 'Drive more revenue through email marketing' }
                                ]
                        },
                        revenues: REVENUE_RANGES
                };
        },
        computed: {
		canProceed() {
			if (this.currentStep === 1) {
				return !!this.formData.orgType;
			}
			if (this.currentStep === 2) {
				return !!this.formData.firstName && this.formData.goals.length > 0;
			}
			if (this.currentStep === 3) {
				const needsUrl = this.formData.orgType === 'brand' || this.formData.orgType === 'individual';
				if (needsUrl && this.urlValid !== true) {
					return false;
				}
                               if (this.formData.orgType !== 'individual' && !this.formData.organizationName) {
                                       return false;
                               }
                                if (this.formData.revenue === null || this.formData.revenue === undefined) {
                                        return false;
                                }
                                return true;
			}
			return false;
		},

                currentGoalOptions() {
                        return this.goalOptions[this.formData.orgType] || [];
                }
        },
        watch: {
                'formData.externalDomain'(newVal) {
                        if (!this.formData.organizationName) {
                                this.prefillOrganizationName();
                        }
                }
        },
        methods: {
                prefillOrganizationName() {
                        if (!this.formData.externalDomain) {
                                return;
                        }
                        let clean = this.formData.externalDomain
                                .replace(/https?:\/\//, '')
                                .replace(/^www\./, '')
                                .split('/')[0];
                        const parts = clean.split('.');
                        let brandPart = '';
                        if (parts.length >= 3 &&
                                parts[parts.length - 2].length <= 3 &&
                                parts[parts.length - 1].length <= 3) {
                                brandPart = parts[parts.length - 3];
                        } else if (parts.length >= 2) {
                                brandPart = parts[parts.length - 2];
                        } else {
                                brandPart = parts[0];
                        }
                        brandPart = brandPart.replace(/[-_]/g, ' ');
                        this.formData.organizationName = brandPart
                                .split(' ')
                                .filter(Boolean)
                                .map(w => w.charAt(0).toUpperCase() + w.slice(1))
                                .join(' ');
                },
                selectUserType(type) {
                        this.formData.orgType = type;
                },

		goToPreviousStep() {
			if (this.currentStep > 1) {
				this.currentStep--;
			}
		},

                handleNextStep() {
                        if (this.currentStep === 1) {
                                this.currentStep = 2;
                        } else if (this.currentStep === 2) {
                                this.currentStep = 3;
                                if (!this.formData.organizationName) {
                                        this.prefillOrganizationName();
                                }
                        } else if (this.currentStep === 3) {
                                this.completeOnboarding();
                        }
                },

		toggleGoal(goalValue) {
			const index = this.formData.goals.indexOf(goalValue);
			if (index > -1) {
				this.formData.goals.splice(index, 1);
			} else {
				this.formData.goals.push(goalValue);
			}
		},

		async completeOnboarding() {
			this.loading = true;
			this.error = null;

			try {
				// Update user profile with the new information
				await this.updateUserProfile();

				// Remove the flags
				localStorage.removeItem('needs_onboarding_completion');
				localStorage.removeItem('signup_form_data');

				// Emit completion event
				this.$emit('complete');
			} catch (err) {
				console.error('Onboarding completion error:', err);
				this.error = err.message || 'Failed to update profile. Please try again.';
			} finally {
				this.loading = false;
			}
		},

		async updateUserProfile() {
			// Get current user info to get user ID
			const userInfo = JSON.parse(localStorage.getItem('userInfo'));
			if (!userInfo || !userInfo.id) {
				throw new Error('User information not available');
			}

			// Update user's firstName in the database
			await UserService.updateUserFLName(userInfo.id, this.formData.firstName, userInfo.lastName || '');

			// Update localStorage with new firstName
			localStorage.setItem('firstName', this.formData.firstName);
			userInfo.firstName = this.formData.firstName;
			localStorage.setItem('userInfo', JSON.stringify(userInfo));

			// Clean up domain
			if (this.formData.externalDomain) {
				this.formData.externalDomain = this.formData.externalDomain.replace(/https?:\/\//, '');
			}

			// Update organization basic settings if needed
			if (this.formData.organizationName || this.formData.externalDomain) {
				const orgUpdateData = {};
				if (this.formData.organizationName) {
					orgUpdateData.name = this.formData.organizationName;
				}
				if (this.formData.externalDomain) {
					orgUpdateData.externalDomain = this.formData.externalDomain;
				}

				const response = await fetch(`${URL_DOMAIN}/organizations/${localStorage.getItem('userOrgId')}`, {
					method: 'PATCH',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`
					},
					body: JSON.stringify(orgUpdateData)
				});

				if (!response.ok) {
					throw new Error('Failed to update organization');
				}
			}

			// Save onboarding data to organization settings using the service
			await OrganizationSettings.updateOrganizationSetting('onboardingQuestion', JSON.stringify({
				firstName: this.formData.firstName,
				goals: this.formData.goals,
				orgType: this.formData.orgType,
				completedAt: new Date().toISOString()
			}));

			// Also save orgType separately for easier access
			await OrganizationSettings.updateOrganizationSetting('orgType', this.formData.orgType);
			await OrganizationSettings.updateOrganizationSetting('revenue', String(this.formData.revenue));

                        // If user provided external domain, trigger onboard/begin
			if (this.formData.externalDomain && !localStorage.getItem('onboard_begin_called' + localStorage.getItem('userOrgId'))) {
				localStorage.setItem('onboard_begin_called' + localStorage.getItem('userOrgId'), 'true');

				const onboardResponse = await fetch(`${URL_DOMAIN}/onboard/begin`, {
					method: 'POST',
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						externalDomain: this.formData.externalDomain
					}),
				});

				if (!onboardResponse.ok) {
					localStorage.removeItem('onboard_begin_called' + localStorage.getItem('userOrgId'));
				}
			}
		},

		async validateUrl() {
			if (!this.formData.externalDomain) return;

			this.urlValidating = true;
			this.urlValid = null;

			try {
				// Clean the URL - remove protocol if present
				let url = this.formData.externalDomain.replace(/https?:\/\//, '').replace(/^www\./, '');

				// Simple URL validation
				const urlPattern = /^[a-zA-Z0-9][a-zA-Z0-9-_.]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
				if (!urlPattern.test(url)) {
					this.urlValid = false;
					return;
				}

				// Update the form data with cleaned URL
				this.formData.externalDomain = url;

				// Try to ping the URL
				const testUrl = `https://${url}`;
				const response = await fetch(testUrl, {
					method: 'HEAD',
					mode: 'no-cors',
					cache: 'no-cache'
				});

				// If we get here without error, URL is accessible
				this.urlValid = true;
			} catch (error) {
				// Try with www prefix
				try {
					const cleanUrl = this.formData.externalDomain.replace(/https?:\/\//, '').replace(/^www\./, '');
					const wwwUrl = `https://www.${cleanUrl}`;
					await fetch(wwwUrl, {
						method: 'HEAD',
						mode: 'no-cors',
						cache: 'no-cache'
					});
					this.urlValid = true;
				} catch (error2) {
					this.urlValid = false;
				}
			} finally {
				this.urlValidating = false;
			}
		},


		getStep2Title() {
			switch (this.formData.orgType) {
				case 'agency':
					return 'Tell us about your agency';
				case 'brand':
					return 'Tell us about your brand';
				case 'individual':
					return 'Tell us about yourself';
				default:
					return 'Organization Details';
			}
		},

		getStep2Description() {
			switch (this.formData.orgType) {
				case 'agency':
					return 'We\'ll help you manage multiple client brands';
				case 'brand':
					return 'We\'ll customize Raleon for your specific needs';
				case 'individual':
					return 'We\'ll set up your personal workspace';
				default:
					return 'Complete your profile';
			}
		}
	},
        mounted() {
                // If we already have an external domain from signup, validate it
                if (this.formData.externalDomain) {
                        this.prefillOrganizationName();
                        this.validateUrl();
                }
        }
};
</script>

<style scoped>
.form-input {
	@apply appearance-none rounded-lg block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500;
}

.btn {
	@apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}
</style>
