<template>
  <div class="space-y-3">
    <!-- Warning for unsynchronized segment -->
    <div
      v-if="campaignReady && !segmentSynced"
      class="p-2 bg-yellow-50 border border-yellow-100 rounded text-xs text-yellow-700 flex items-start"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-yellow-500 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>Warning: Your segment is not synced to Klaviyo. Proceeding will create a campaign without customer data.</span>
    </div>

    <!-- Loading button -->
    <button disabled class="px-3 py-1.5 text-sm rounded-lg flex items-center justify-center relative overflow-hidden border border-blue-200 bg-blue-50 animate-pulse-subtle min-w-[200px]">
      <div class="absolute inset-0 animate-gradient" />
      <div class="relative flex items-center z-10">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
        <span class="font-medium text-blue-700">{{ klaviyoStatus?.step || 'Creating Klaviyo Campaign...' }}</span>
      </div>
    </button>

    <!-- Progress Steps -->
    <div class="bg-white border border-blue-100 rounded-lg p-3 space-y-2">
      <h5 class="text-xs font-medium text-blue-800 mb-2">Campaign Creation Progress</h5>
      <ProgressStep :index="1" label="Preparing segment data" :current-step="currentStep" />
      <ProgressStep :index="2" label="Creating campaign template" :current-step="currentStep" />
      <ProgressStep :index="3" label="Setting up campaign settings" :current-step="currentStep" />
      <ProgressStep :index="4" label="Finalizing campaign creation" :current-step="currentStep" />
    </div>

    <!-- Real-time Klaviyo API Status -->
    <div v-if="klaviyoStatus" class="bg-white border border-blue-100 rounded-lg p-3">
      <div class="flex items-center justify-between mb-2">
        <h5 class="text-xs font-medium text-blue-800">Real-time Status</h5>
        <span class="text-xs px-2 py-0.5 rounded-full" :class="statusBadgeClass">{{ klaviyoStatus.status }}</span>
      </div>

      <div v-if="klaviyoStatus.step" class="mb-3">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-blue-600 rounded-full animate-pulse mr-2" />
          <p class="text-sm font-medium text-blue-800">{{ klaviyoStatus.step }}</p>
        </div>
      </div>
      <p v-if="processStartTime" class="text-xs text-gray-500 mt-1">Started: {{ processStartTime }}</p>
      <p v-if="processDuration" class="text-xs text-gray-500">Duration: {{ processDuration }}</p>

      <div v-if="klaviyoStatus.error" class="p-2 bg-red-50 rounded border border-red-100 mt-2">
        <p class="text-xs text-red-600">{{ klaviyoStatus.error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import ProgressStep from './ProgressStep.ts.vue';

interface Props {
  klaviyoStatus: Record<string, any> | null;
  currentStep: number;
  campaignReady: boolean;
  segmentSynced: boolean;
}

const props = defineProps<Props>();

const statusBadgeClass = computed(() => {
  const status = props.klaviyoStatus?.status;
  if (status === 'processing') return 'bg-blue-100 text-blue-800';
  if (status === 'completed') return 'bg-green-100 text-green-800';
  if (status === 'failed') return 'bg-red-100 text-red-800';
  return 'bg-gray-100 text-gray-800';
});

const processStartTime = computed(() => {
  if (!props.klaviyoStatus?.startTime) return null;
  const date = new Date(props.klaviyoStatus.startTime);
  return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
});

// live timer
const now = ref(Date.now());
let interval: ReturnType<typeof setInterval> | null = null;

onMounted(() => {
  if (props.klaviyoStatus?.startTime) {
    interval = setInterval(() => (now.value = Date.now()), 1000);
  }
});

onUnmounted(() => {
  if (interval) clearInterval(interval);
});

const processDuration = computed(() => {
  if (!props.klaviyoStatus?.startTime) return null;
  const elapsed = now.value - new Date(props.klaviyoStatus.startTime).getTime();
  if (elapsed < 60000) return `${Math.floor(elapsed / 1000)}s`;
  const minutes = Math.floor(elapsed / 60000);
  const seconds = Math.floor((elapsed % 60000) / 1000);
  return `${minutes}m ${seconds}s`;
});
</script>

<style scoped>
.animate-gradient {
  background-size: 200% 100% !important;
  animation: gradientMove 1.5s infinite linear !important;
  background-image: linear-gradient(to right, #93c5fd, #dbeafe, #ffffff, #dbeafe, #93c5fd) !important;
}

@keyframes gradientMove {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 0%; }
}

.animate-pulse-subtle {
  animation: pulseShadow 2s ease-in-out infinite;
}

@keyframes pulseShadow {
  0%, 100% { box-shadow: 0 0 0 rgba(96, 165, 250, 0); }
  50% { box-shadow: 0 0 8px rgba(96, 165, 250, 0.6); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4,0,0.6,1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
