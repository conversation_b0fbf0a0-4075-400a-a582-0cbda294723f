<template>
  <!-- Implementation identical to previous version -->
  <div class="border rounded-lg overflow-hidden">
    <div class="p-4 bg-gray-50 border-b">
      <h3 class="font-medium text-gray-800">Campaign Checklist</h3>
      <p class="text-sm text-gray-500 mt-1">Verify all items before launching the campaign</p>
    </div>

    <div class="p-4">
      <ul class="space-y-4">
        <!-- Segment sync item -->
        <li class="flex items-start gap-3">
          <StatusIcon :active="segmentSynced" />
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <h4 class="font-medium" :class="segmentSynced ? 'text-gray-900' : 'text-gray-600'">Segment Synced to Klaviyo</h4>
                <span class="ml-2 text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded">Optional</span>
              </div>
              <StatusBadge :state="segmentSynced ? 'complete' : 'pending'" :labels="{ complete: 'Complete', pending: 'Not Synced' }" />
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ segmentSynced ? `Synced on ${formattedSyncDate}` : 'Go to the Segment task to sync your audience with Klaviyo' }}</p>
            <div v-if="segmentSynced" class="mt-2 text-sm">
              <div class="flex items-center gap-2">
                <EyeIcon class="h-4 w-4 text-blue-500" />
                <span>{{ customersInSegment }} customers in segment</span>
              </div>
            </div>
            <div v-if="!segmentSynced" class="mt-3">
              <PrimaryButton cta="Go to Segment Task" size="xs" @click="navigateToSegmentTask" />
            </div>
          </div>
        </li>

        <!-- Date validation -->
        <div v-if="!isScheduledDateValid && task?.campaign?.scheduledDate" class="mt-2 p-2 bg-yellow-50 border border-yellow-100 rounded-md flex items-center text-xs text-yellow-700">
          <WarningIcon class="h-4 w-4 text-yellow-500 mr-2" />
          Campaign date must be today or in the future
        </div>

        <!-- Email design approved -->
        <li class="flex items-start gap-3">
          <StatusIcon :active="emailDesignApproved" />
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h4 class="font-medium" :class="emailDesignApproved ? 'text-gray-900' : 'text-gray-600'">Email Design Approved</h4>
              <StatusBadge :state="emailDesignApproved ? 'complete' : 'pending'" />
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ emailDesignApproved ? 'Email template has been generated and approved' : 'Go to the Content task and make sure you have an email generated' }}</p>
          </div>
        </li>

        <!-- Campaign ready -->
        <li class="flex items-start gap-3">
          <StatusIcon :active="campaignReady" :processing="processing" />
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <h4 class="font-medium" :class="campaignReady ? 'text-gray-900' : 'text-gray-600'">Campaign Ready</h4>
              <template v-if="processing">
                <ProcessingBadge />
              </template>
              <template v-else>
                <StatusBadge :state="campaignReady ? 'complete' : 'pending'" />
              </template>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ campaignReadyMessage }}</p>

            <!-- Action area -->
            <div class="mt-3">
              <!-- Mgmt -->
              <div v-if="showManagementButtons" class="flex space-x-3 mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <PrimaryButton cta="View In Klaviyo" size="xs" @click="viewInKlaviyo" />
                <LightSecondaryButton cta="Re-sync to Klaviyo" size="xs" @click="() => emit('open-resync', 'hybrid')" />
              </div>

              <!-- Creation -->
              <div v-if="showCreationButtons" class="space-y-4">
                <div class="text-left p-3 bg-green-50 rounded-lg border border-green-200">
                  <div class="flex items-center gap-2 mb-2">
                    <PrimaryButton cta="Create Editable Campaign" size="xs" :disabled="!campaignReady" @click="startExportHybrid" />
                    <span class="text-xs px-2 py-0.5 bg-green-200 text-green-800 rounded-full font-medium">Recommended</span>
                  </div>
                  <p class="text-xs text-green-700 leading-relaxed">Creates a campaign with draggable regions that can be edited in Klaviyo's visual editor</p>
                </div>

                <div class="text-left pt-4">
                  <div class="mb-3">
                    <LightSecondaryButton cta="Create HTML Only Campaign" :isDisabled="!emailDesignApproved" @click="startExportHtml" />
                  </div>
                  <p class="text-xs text-gray-500 leading-relaxed"><span class="font-medium">Having trouble?</span> Use HTML only mode for basic campaign creation.<br />Email will only be editable via code in Klaviyo.</p>
                </div>
              </div>

              <!-- Processing -->
              <ProcessingPanel v-if="processing" :klaviyo-status="klaviyoStatus" :current-step="currentStep" :campaign-ready="campaignReady" :segment-synced="segmentSynced" />
            </div>
          </div>
        </li>

        <!-- Copy HTML -->
        <li class="flex items-start gap-3">
          <StatusIcon :active="false" />
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <h4 class="font-medium text-gray-600">Copy Email HTML</h4>
                <span class="ml-2 text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded">Optional</span>
              </div>
            </div>
            <p class="text-sm text-gray-500 mt-1">Copy the HTML code to use with any email service provider</p>
            <div v-if="task.emailHtml" class="mt-3">
              <LightSecondaryButton :cta="htmlCopied ? 'Copied!' : 'Copy HTML'" @click="copyHtmlToClipboard" />
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onActivated, onDeactivated, onUnmounted } from 'vue';

import PrimaryButton from '../../PrimaryButton.ts.vue';
import LightSecondaryButton from '../../LightSecondaryButton.ts.vue';
import StatusIcon from './StatusIcon.ts.vue';
import StatusBadge from './StatusBadge.ts.vue';
import ProcessingBadge from './ProcessingBadge.ts.vue';
import ProcessingPanel from './ProcessingPanel.ts.vue';

import { TaskType } from '../../../constants/TaskTypes';

import EyeIcon from './icons/EyeIcon.vue';
import WarningIcon from './icons/WarningIcon.vue';

interface Props {
  task: Record<string, any>;
  segment?: Record<string, any>;
  taskSteps?: Array<Record<string, any>>;
  klaviyoStatus?: Record<string, any> | null;
  isCreating?: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-task', step: any): void;
  (e: 'export-klaviyo'): void;
  (e: 'export-klaviyo-html'): void;
  (e: 'open-resync', variant: 'hybrid' | 'html'): void;
}>();

const htmlCopied = ref(false);
const currentStep = ref(0);
let processTimerInterval: ReturnType<typeof setInterval> | null = null;

const segmentSynced = computed(() => !!props.segment && !!props.segment.externalId);

const formattedSyncDate = computed(() => {
  if (!props.segment?.externalSyncDate) return '';
  const date = new Date(props.segment.externalSyncDate);
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' });
});

const customersInSegment = computed(() => props.segment?.aggregates?.totalCount || props.segment?.customers?.length || 0);

const emailDesignApproved = computed(() => !!props.task && !!props.task.emailHtml);

const isScheduledDateValid = computed(() => {
  if (!props.task?.campaign?.scheduledDate) return false;
  const scheduledDate = new Date(props.task.campaign.scheduledDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return !isNaN(scheduledDate.getTime()) && scheduledDate >= today;
});

const campaignReady = computed(() => emailDesignApproved.value && segmentSynced.value && isScheduledDateValid.value);

const processing = computed(() => props.isCreating || props.klaviyoStatus?.status === 'processing');

// Transitional status when creation just completed or still processing
const isInTransition = computed(() => {
  return props.klaviyoStatus?.status === 'completed' || processing.value;
});

// Buttons visibility helpers
const showManagementButtons = computed(() => {
  return (
    (props.task.klaviyoCampaignId || props.klaviyoStatus?.status === 'completed') &&
    !processing.value
  );
});

const showCreationButtons = computed(() => {
  return !props.task.klaviyoCampaignId && !isInTransition.value;
});

const campaignReadyMessage = computed(() => {
  if (processing.value) return `Creating campaign in Klaviyo: ${props.klaviyoStatus?.step || 'Initializing...'}`;
  if (campaignReady.value) {
    if (props.task.klaviyoCampaignId) return 'Your campaign has been exported to Klaviyo';
    if (!segmentSynced.value) return 'Your campaign is ready to be sent to Klaviyo. Note: Segment is not synced to Klaviyo';
    return 'Your campaign is ready to be sent to Klaviyo';
  }
  return 'Complete all required tasks before exporting to Klaviyo';
});

const segmentTask = computed(() => props.taskSteps?.find((s) => s.taskTypeId === TaskType.SEGMENT));

// Step mapping helper
function mapStatusToStep(stepStr?: string | null): number {
  if (!stepStr) return 0;
  const s = stepStr.toLowerCase();
  if (s.includes('prepar') || s.includes('validat')) return 1;
  if (s.includes('processing html') || s.includes('template')) return 2;
  if (s.includes('setting') || s.includes('configur')) return 3;
  if (s.includes('finalizing') || s.includes('complet')) return 4;
  return 0;
}

watch(() => props.klaviyoStatus?.status, (newStatus) => {
  if (newStatus === 'processing') currentStep.value = mapStatusToStep(props.klaviyoStatus?.step);
});

watch(() => props.klaviyoStatus?.step, (newStep) => {
  if (processing.value) currentStep.value = mapStatusToStep(newStep);
});

function navigateToSegmentTask() {
  if (segmentTask.value) emit('select-task', segmentTask.value);
}

function startExportHybrid() {
  if (campaignReady.value && !processing.value && !props.klaviyoStatus?.status) {
    currentStep.value = 1;
    emit('export-klaviyo');
  }
}

function startExportHtml() {
  if (emailDesignApproved.value && !processing.value && !props.klaviyoStatus?.status) {
    currentStep.value = 1;
    emit('export-klaviyo-html');
  }
}

function viewInKlaviyo() {
  if (props.task.klaviyoCampaignId) window.open(`https://www.klaviyo.com/campaign/${props.task.klaviyoCampaignId}/wizard/1`, '_blank');
}

function copyHtmlToClipboard() {
  if (!props.task.emailHtml) return;
  navigator.clipboard.writeText(props.task.emailHtml).then(() => {
    htmlCopied.value = true;
    setTimeout(() => (htmlCopied.value = false), 2000);
  });
}

onMounted(() => {
  if (processing.value) currentStep.value = mapStatusToStep(props.klaviyoStatus?.step);
});
</script>

<style scoped>
.animate-pulse-subtle {
  animation: pulseShadow 2s ease-in-out infinite;
}
@keyframes pulseShadow {
  0%, 100% { box-shadow: 0 0 0 rgba(96, 165, 250, 0); }
  50% { box-shadow: 0 0 8px rgba(96, 165, 250, 0.6); }
}
</style>
