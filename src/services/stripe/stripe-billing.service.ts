import {BindingScope, injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import Strip<PERSON> from 'stripe';
import {OrganizationPlanRepository} from '../../repositories';
import {OrganizationPlan, Plan} from '../../models';
import {BillingService} from '../shopify/billing.service';

@injectable({scope: BindingScope.TRANSIENT})
export class StripeBillingService {
  private stripe: Stripe;
  private readonly RESTRICTED_API_KEY = '***********************************************************************************************************';
  private readonly TEST_API_KEY = 'rk_test_51MTp7XIox80A0GuIecPlRIVnj7jUSoymHUt046pfnYjo9g3czqihRVmaimalEcKrzAZY2VquLHkRCLkDDNlHvpc400hqc0KAXz';

  constructor(
	@repository(OrganizationPlanRepository) private orgPlanRepository: OrganizationPlanRepository,
	@service(BillingService) private billingService: BillingService,
  ) {
    // Initialize Stripe with the appropriate key based on environment
    const apiKey = this.getApiKey();
    this.stripe = new Stripe(apiKey, {
      apiVersion: '2025-04-30.basil', // Use the latest API version
    });
  }

  /**
   * Get the appropriate API key based on environment
   */
  private getApiKey(): string {
    // Use environment variable if available, fall back to restricted key
    if (process.env.NODE_ENV === 'production') {
      return process.env.STRIPE_SECRET_KEY || this.RESTRICTED_API_KEY;
    } else {
      // For development/test environments, use test key from env variables
      const testKey = process.env.STRIPE_TEST_KEY || this.TEST_API_KEY;
      if (!testKey) {
        console.warn('No Stripe test key found. Please set STRIPE_TEST_KEY environment variable for testing.');
      }
      return testKey || 'sk_test_placeholder'; // This will fail safely if not replaced
    }
  }

  /**
   * Create a subscription in Stripe
   */
  private async createSubscription(
    orgId: number,
    amount: number,
    name: string,
    interval: string,
    returnUrl: string,
    test: boolean,
  ) {
    console.log(`Creating Stripe subscription: ${name} for ${orgId} with amount: ${amount}`);

    try {
      // Find or create customer
      const customer = await this.findOrCreateCustomer(orgId);

      // Find or create product
      const product = await this.findOrCreateProduct(name);

      // Find or create price
      const price = await this.findOrCreatePrice(
        amount,
        product.id,
        this.convertIntervalToStripe(interval)
      );

      // Create checkout session
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        customer: customer.id,
        line_items: [
          {
            price: price.id,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${returnUrl}?success=true&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${returnUrl}?canceled=true`,
        metadata: {
          orgId: orgId.toString(),
          planName: name,
          planId: name.toLowerCase().replace(/\s+/g, '-'),
        },
        allow_promotion_codes: true,
        billing_address_collection: 'auto',
        subscription_data: {
          metadata: {
            orgId: orgId.toString(),
            planName: name,
          },
          trial_period_days: test ? 30 : undefined,
        }
      });

      console.log(`Checkout session created: ${session.id}, URL: ${session.url}`);

      // Return in a format compatible with the existing code
      return {
        data: {
          appSubscriptionCreate: {
            appSubscription: {
              id: session.id
            },
            confirmationUrl: session.url
          }
        },
		confirmationUrl: session.url
      };
    } catch (error) {
      console.error('Error creating Stripe subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription at period end using Stripe's cancel_at_period_end feature
   */
  async cancelSubscriptionAtPeriodEnd(
    orgId: number,
    subscriptionId: string
  ) {
    console.log(`Setting cancel_at_period_end=true for Stripe subscription: ${subscriptionId} for org ${orgId}`);

    try {
      // Update the subscription to cancel at period end
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });

      // Get the period end date using the same logic as other billing date detection
      let periodEndDate = null;

      // Try to get current_period_end from subscription items first
      if ((updatedSubscription as any).items?.data?.length > 0) {
        const firstItem = (updatedSubscription as any).items.data[0];
        if (firstItem.current_period_end) {
          periodEndDate = new Date(firstItem.current_period_end * 1000);
          console.log(`Found period end in subscription item: ${periodEndDate.toISOString()}`);
        }
      }

      // Fallback: try subscription level
      if (!periodEndDate && (updatedSubscription as any).current_period_end) {
        periodEndDate = new Date((updatedSubscription as any).current_period_end * 1000);
        console.log(`Found period end at subscription level: ${periodEndDate.toISOString()}`);
      }

      // Final fallback: use billing_cycle_anchor + 1 month
      if (!periodEndDate && (updatedSubscription as any).billing_cycle_anchor) {
        const billingAnchor = new Date((updatedSubscription as any).billing_cycle_anchor * 1000);
        periodEndDate = new Date(billingAnchor);
        periodEndDate.setMonth(periodEndDate.getMonth() + 1);
        console.log(`Using billing cycle anchor + 1 month: ${periodEndDate.toISOString()}`);
      }

      // Last resort fallback
      if (!periodEndDate) {
        console.warn(`Could not determine period end date for subscription ${subscriptionId}. Using 30 days from now.`);
        periodEndDate = new Date();
        periodEndDate.setDate(periodEndDate.getDate() + 30);
        console.log(`Using fallback period end date: ${periodEndDate.toISOString()}`);
      }

      console.log(`Subscription ${subscriptionId} set to cancel at period end: ${periodEndDate.toISOString()}`);

      return {
        data: {
          appSubscriptionCancel: {
            appSubscription: {
              status: 'SCHEDULED_CANCELLATION',
              id: updatedSubscription.id
            }
          }
        },
        periodEndDate: periodEndDate
      };
    } catch (error) {
      console.error(`Error setting cancel_at_period_end for subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Reactivate a subscription by removing cancel_at_period_end
   */
  async reactivateSubscription(
    orgId: number,
    subscriptionId: string
  ) {
    console.log(`Removing cancel_at_period_end for Stripe subscription: ${subscriptionId} for org ${orgId}`);

    try {
      // Update the subscription to remove cancel_at_period_end
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
      });

      console.log(`Subscription ${subscriptionId} reactivated, will continue beyond period end`);

      return {
        data: {
          appSubscriptionUpdate: {
            appSubscription: {
              status: 'ACTIVE',
              id: updatedSubscription.id
            }
          }
        }
      };
    } catch (error) {
      console.error(`Error reactivating subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel a subscription in Stripe immediately
   */
  async unsubscribe(
    orgId: number,
    subscriptionId: string
  ) {
    console.log(`Canceling Stripe subscription: ${subscriptionId} for ${orgId}`);

    try {
      // Cancel the subscription
      const canceledSubscription = await this.stripe.subscriptions.cancel(subscriptionId, {
        prorate: true,
      });

      console.log(`Subscription ${subscriptionId} canceled: ${canceledSubscription.status}`);

      // Return in a format compatible with the existing code
      return {
        data: {
          appSubscriptionCancel: {
            appSubscription: {
              status: 'CANCELLED',
              id: canceledSubscription.id
            }
          }
        }
      };
    } catch (error) {
      console.error(`Error canceling subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel subscription immediately in Stripe but return the original billing end date for internal scheduling
   */
  async cancelSubscriptionWithDelayedInternalTransition(
    orgId: number,
    subscriptionId: string
  ) {
    console.log(`Cancelling Stripe subscription immediately but scheduling internal transition: ${subscriptionId} for ${orgId}`);

    try {
      // First, get the current billing period end date before cancelling
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      console.log(`Retrieving billing period for subscription ${subscriptionId} (status: ${subscription.status})`);

      let originalBillingEndDate = null;

      // The current_period_end is in the subscription items, not at the subscription level
      if ((subscription as any).items?.data?.length > 0) {
        const firstItem = (subscription as any).items.data[0];
        if (firstItem.current_period_end) {
          originalBillingEndDate = new Date(firstItem.current_period_end * 1000);
          console.log(`Found billing period end in subscription item: ${originalBillingEndDate.toISOString()}`);
        }
      }

      // Fallback: try subscription level (though this seems to not exist in newer Stripe versions)
      if (!originalBillingEndDate && (subscription as any).current_period_end) {
        originalBillingEndDate = new Date((subscription as any).current_period_end * 1000);
        console.log(`Found billing period end at subscription level: ${originalBillingEndDate.toISOString()}`);
      }

      // Fallback: if it's a trial subscription, use trial end
      if (!originalBillingEndDate && (subscription as any).trial_end) {
        originalBillingEndDate = new Date((subscription as any).trial_end * 1000);
        console.log(`Using trial end date: ${originalBillingEndDate.toISOString()}`);
      }

      // Final fallback: use billing_cycle_anchor + 1 month (for monthly subscriptions)
      if (!originalBillingEndDate && (subscription as any).billing_cycle_anchor) {
        const billingAnchor = new Date((subscription as any).billing_cycle_anchor * 1000);
        originalBillingEndDate = new Date(billingAnchor);

        // Add one billing period (assume monthly for now)
        originalBillingEndDate.setMonth(originalBillingEndDate.getMonth() + 1);
        console.log(`Using billing cycle anchor + 1 month: ${originalBillingEndDate.toISOString()}`);
      }

      // Last resort fallback
      if (!originalBillingEndDate) {
        console.warn(`Could not determine original billing end date for subscription ${subscriptionId}. Subscription status: ${subscription.status}`);
        originalBillingEndDate = new Date();
        originalBillingEndDate.setDate(originalBillingEndDate.getDate() + 30);
        console.log(`Using fallback billing end date (30 days from now): ${originalBillingEndDate.toISOString()}`);
      }

      // Cancel the subscription immediately in Stripe (only if it's not already cancelled)
      let canceledSubscription;
      if (subscription.status === 'canceled') {
        console.log(`Subscription ${subscriptionId} is already cancelled in Stripe`);
        canceledSubscription = subscription;
      } else {
        canceledSubscription = await this.stripe.subscriptions.cancel(subscriptionId, {
          prorate: true,
        });
        console.log(`Subscription ${subscriptionId} canceled immediately in Stripe: ${canceledSubscription.status}`);
      }

      return {
        data: {
          appSubscriptionCancel: {
            appSubscription: {
              status: 'CANCELLED',
              id: canceledSubscription.id
            }
          }
        },
        originalBillingEndDate: originalBillingEndDate
      };
    } catch (error) {
      console.error(`Error cancelling subscription ${subscriptionId} with delayed internal transition:`, error);
      throw error;
    }
  }

  /**
   * Update subscription at billing cycle (for scheduled downgrades)
   */
  async updateSubscriptionAtBillingCycle(
    orgId: number,
    subscriptionId: string,
    planName: string,
    newAmount: number,
    interval: string = 'MONTHLY'
  ) {
    console.log(`Updating subscription ${subscriptionId} at billing cycle for org ${orgId} to ${planName} ($${newAmount})`);

    try {
      // Find or create product for the plan
      const product = await this.findOrCreateProduct(planName);

      // Find or create a price for the plan
      const price = await this.findOrCreatePrice(
        newAmount,
        product.id,
        this.convertIntervalToStripe(interval)
      );

      // Update the subscription without proration (since we're at billing cycle boundary)
      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: (await this.stripe.subscriptions.retrieve(subscriptionId)).items.data[0].id,
              price: price.id,
            },
          ],
          proration_behavior: 'none', // No proration since we're at billing cycle
          metadata: {
            updatedByOrgId: orgId.toString(),
            updateTimestamp: new Date().toISOString(),
            scheduledDowngrade: 'true'
          },
        }
      );

      console.log(`Subscription ${subscriptionId} updated at billing cycle to ${planName}`);
      return updatedSubscription;
    } catch (error) {
      console.error(`Error updating subscription ${subscriptionId} at billing cycle:`, error);
      throw error;
    }
  }

  /**
   * Update an existing subscription (for plan changes with proration)
   */
  private async updateSubscription(
    orgId: number,
    subscriptionId: string,
    newPriceId: string,
  ) {
    console.log(`Updating subscription ${subscriptionId} to new price ${newPriceId} for org ${orgId}`);

    try {
      // Get the current subscription to find the subscription item ID
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      if (!subscription || !subscription.items.data.length) {
        throw new Error(`No subscription items found for subscription ${subscriptionId}`);
      }

      const itemId = subscription.items.data[0].id;

      // Update the subscription with proration and clear cancel_at_period_end
      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: itemId,
              price: newPriceId,
            },
          ],
          proration_behavior: 'always_invoice', // Enable automatic proration
          cancel_at_period_end: false, // Clear any pending cancellation
          metadata: {
            updatedByOrgId: orgId.toString(),
            updateTimestamp: new Date().toISOString(),
          },
        }
      );

      console.log(`Subscription ${subscriptionId} updated to price ${newPriceId} and cleared cancel_at_period_end`);

	//   const match = await this.orgPlanRepository.find({
	// 	where: {
	// 		subscriptionId: subscriptionId,
	// 	}
	//   });

	//   if (match.length > 0) {
	// 	this.cancelLegacyShopifySubscriptions(orgId).catch(err => {
	// 		console.error(`Error cancelling legacy Shopify subscriptions:`, err);
	// 	});

	// 	await this.orgPlanRepository.updateById(match[0].id, {
	// 		status: 'ACTIVE',
	// 		subscriptionId: updatedSubscription.id,
	// 		confirmationUrl: undefined,
	// 	});

	// 	// update all other plans to CANCELLED
	// 	await this.orgPlanRepository.updateAll({
	// 		status: 'CANCELLED',
	// 		subscriptionId: undefined,
	// 		confirmationUrl: undefined,
	// 	}, {
	// 		orgId: orgId,
	// 		id: {
	// 			nin: [match[0].id]
	// 		}
	// 	});
	//   }


      return {
        data: {
          subscriptionUpdate: {
            subscription: {
              id: updatedSubscription.id,
              status: updatedSubscription.status,
            }
          }
        },
		confirmationUrl: undefined
      };
    } catch (error) {
      console.error(`Error updating subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

//   /**
//    * Change a customer's plan with proration
//    */
//   private async changePlan(
//     orgId: number,
//     currentSubscriptionId: string,
//     newPlanName: string,
//     newPlanAmount: number,
//     newPlanInterval: string,
//     revenue?: number,
//   ) {
//     console.log(`Changing plan for org ${orgId} from subscription ${currentSubscriptionId} to ${newPlanName}`);

//     try {
//       // Adjust price based on revenue if needed
//       let finalAmount = newPlanAmount;
//       if (revenue) {
//         // This is a simplistic implementation; you might have more complex rules
//         if (revenue > 1000000) {
//           finalAmount = finalAmount * 1.5;
//         } else if (revenue > 500000) {
//           finalAmount = finalAmount * 1.25;
//         }
//       }

//       // Find or create product for the plan
//       const product = await this.findOrCreateProduct(newPlanName);

//       // Find or create a price for the plan
//       const price = await this.findOrCreatePrice(
//         finalAmount,
//         product.id,
//         this.convertIntervalToStripe(newPlanInterval)
//       );

//       // Update the subscription with the new price
//       return this.updateSubscription(orgId, currentSubscriptionId, price.id);

//     } catch (error) {
//       console.error(`Error changing plan for subscription ${currentSubscriptionId}:`, error);
//       throw error;
//     }
//   }

  /**
   * Find or create a customer in Stripe
   */
  private async findOrCreateCustomer(orgId: number) {
    try {
      // Try to find the customer first
      const customers = await this.stripe.customers.list({
        limit: 100,
      });

      const existingCustomer = customers.data.find(
        customer => customer.metadata && customer.metadata.orgId === orgId.toString()
      );

      if (existingCustomer) {
        console.log(`Found existing customer for org ${orgId}: ${existingCustomer.id}`);
        return existingCustomer;
      }

      // If customer doesn't exist, create one
      // Note: In a real implementation, you would fetch organization details here
      const customer = await this.stripe.customers.create({
        metadata: {
          orgId: orgId.toString(),
          createdAt: new Date().toISOString(),
        },
        description: `Organization ${orgId}`,
      });

      console.log(`Created new customer for org ${orgId}: ${customer.id}`);
      return customer;
    } catch (error) {
      console.error(`Error finding/creating customer for org ${orgId}:`, error);
      throw error;
    }
  }


  /**
   * Determine if a plan change is a downgrade based on price comparison
   */
  private async isDowngrade(orgId: number, newPlanAmount: number): Promise<boolean> {
    try {
      // Find the current active plan
      const currentOrgPlan = await this.orgPlanRepository.findOne({
        where: {
          orgId: orgId,
          status: 'ACTIVE'
        }
      });

      if (!currentOrgPlan) {
        // No current plan, so this is not a downgrade
        return false;
      }

      // Get the current plan's effective price (priceOverride or plan price)
      const currentPrice = currentOrgPlan.priceOverride || 0;

      // If we can't determine current price, assume it's not a downgrade
      if (currentPrice === 0) {
        return false;
      }

      // Compare prices - downgrade if new price is lower
      return newPlanAmount < currentPrice;
    } catch (error) {
      console.error(`Error determining if plan change is downgrade for org ${orgId}:`, error);
      return false; // Default to not a downgrade if we can't determine
    }
  }

  /**
   * Get the original billing period end date from Stripe subscription before making changes
   */
  private async getOriginalBillingEndDate(subscriptionId: string): Promise<Date | null> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      console.log(`Retrieving original billing period for subscription ${subscriptionId} (status: ${subscription.status})`);

      let originalBillingEndDate = null;

      // The current_period_end is in the subscription items, not at the subscription level
      if ((subscription as any).items?.data?.length > 0) {
        const firstItem = (subscription as any).items.data[0];
        if (firstItem.current_period_end) {
          originalBillingEndDate = new Date(firstItem.current_period_end * 1000);
          console.log(`Found billing period end in subscription item: ${originalBillingEndDate.toISOString()}`);
        }
      }

      // Fallback: try subscription level (though this seems to not exist in newer Stripe versions)
      if (!originalBillingEndDate && (subscription as any).current_period_end) {
        originalBillingEndDate = new Date((subscription as any).current_period_end * 1000);
        console.log(`Found billing period end at subscription level: ${originalBillingEndDate.toISOString()}`);
      }

      // Fallback: if it's a trial subscription, use trial end
      if (!originalBillingEndDate && (subscription as any).trial_end) {
        originalBillingEndDate = new Date((subscription as any).trial_end * 1000);
        console.log(`Using trial end date: ${originalBillingEndDate.toISOString()}`);
      }

      // Final fallback: use billing_cycle_anchor + 1 month (for monthly subscriptions)
      if (!originalBillingEndDate && (subscription as any).billing_cycle_anchor) {
        const billingAnchor = new Date((subscription as any).billing_cycle_anchor * 1000);
        originalBillingEndDate = new Date(billingAnchor);

        // Add one billing period (assume monthly for now)
        originalBillingEndDate.setMonth(originalBillingEndDate.getMonth() + 1);
        console.log(`Using billing cycle anchor + 1 month: ${originalBillingEndDate.toISOString()}`);
      }

      // Last resort fallback
      if (!originalBillingEndDate) {
        console.warn(`Could not determine original billing end date for subscription ${subscriptionId}. Subscription status: ${subscription.status}`);
        originalBillingEndDate = new Date();
        originalBillingEndDate.setDate(originalBillingEndDate.getDate() + 30);
        console.log(`Using fallback billing end date (30 days from now): ${originalBillingEndDate.toISOString()}`);
      }

      return originalBillingEndDate;
    } catch (error) {
      console.error(`Error getting original billing date for subscription ${subscriptionId}:`, error);
      return null;
    }
  }

  /**
   * Get the next billing cycle date from Stripe subscription
   * @deprecated Use getOriginalBillingEndDate instead
   */
  private async getNextBillingDate(subscriptionId: string): Promise<Date | null> {
    return this.getOriginalBillingEndDate(subscriptionId);
  }

  /**
   * Migrate a customer from one plan to another, handling all billing state properly
   */
  async migrateToPlan(
    orgId: number,
	newPlan: Plan,
    newPlanAmount: number,
    newPlanInterval: string = 'MONTHLY',
  ): Promise<any> {
	const newPlanId = newPlan.id;
	const newPlanName = newPlan.name;

	// Check if this is a downgrade
	const isDowngradeChange = await this.isDowngrade(orgId, newPlanAmount);
	console.log(`Plan change for org ${orgId}: ${isDowngradeChange ? 'DOWNGRADE' : 'UPGRADE'} to ${newPlanName} ($${newPlanAmount})`);

	// Check if user is reselecting their current active plan
	const currentActivePlan = await this.orgPlanRepository.findOne({
		where: {
			orgId: orgId,
			status: 'ACTIVE'
		}
	});

	const isReactivatingCurrentPlan = currentActivePlan && currentActivePlan.planId === newPlanId;

	// Check for trial conversion case: ACTIVE plan with same planId but no subscriptionId
	const isTrialConversion = currentActivePlan &&
		currentActivePlan.planId === newPlanId &&
		!currentActivePlan.subscriptionId;

	if (isTrialConversion) {
		console.log(`Trial conversion detected for org ${orgId}: converting ACTIVE plan ${newPlanName} (no subscriptionId) to subscription`);

		// Get existing Stripe subscriptions for this organization
		const existingStripeSubscriptions = await this.getOrgSubscriptions(orgId);
		const activeStripeSubscriptions = existingStripeSubscriptions.filter(
			subscription => subscription.status === 'active' || subscription.status === 'trialing'
		);

		// Clear any scheduled downgrades first
		await this.orgPlanRepository.updateAll({
			scheduledEnd: undefined,
			scheduledStart: undefined
		}, {
			orgId: orgId
		});

		// Cancel any PENDING_NEXT_RENEWAL plans
		await this.orgPlanRepository.updateAll({
			status: 'CANCELLED',
			scheduledStart: undefined,
			scheduledEnd: undefined,
			subscriptionId: undefined,
			confirmationUrl: undefined
		}, {
			orgId: orgId,
			status: 'PENDING_NEXT_RENEWAL'
		});

		try {
			// Find or create product and price for the plan
			const product = await this.findOrCreateProduct(newPlanName);
			const price = await this.findOrCreatePrice(
				newPlanAmount,
				product.id,
				this.convertIntervalToStripe(newPlanInterval)
			);

			if (activeStripeSubscriptions.length > 0) {
				// Case 1: There's an existing Stripe subscription - update it and assign to the plan
				const existingSubscription = activeStripeSubscriptions[0];
				console.log(`Found existing Stripe subscription ${existingSubscription.id} for trial conversion`);

				// Update the existing subscription to the new plan/price
				await this.updateSubscription(orgId, existingSubscription.id, price.id);

				// Update the current plan with the subscription ID
				await this.orgPlanRepository.updateById(currentActivePlan.id, {
					subscriptionId: existingSubscription.id,
					priceOverride: newPlanAmount,
					status: 'ACTIVE'
				});

				console.log(`Trial conversion completed: assigned subscription ${existingSubscription.id} to plan ${currentActivePlan.id}`);

				return {
					data: {
						subscriptionUpdate: {
							subscription: {
								id: existingSubscription.id,
								status: existingSubscription.status,
							}
						}
					},
					confirmationUrl: '/loyalty/settings/plans'
				};
			} else {
				// Case 2: No existing Stripe subscription - create a new one
				console.log(`No existing Stripe subscription found for trial conversion, creating new subscription`);

				const result = await this.createSubscription(
					orgId,
					newPlanAmount,
					newPlanName,
					newPlanInterval,
					'https://app.raleon.io/loyalty/settings/plans',
					false
				);

				// Update the current plan with the new subscription details
				await this.orgPlanRepository.updateById(currentActivePlan.id, {
					status: 'PENDING', // Will be updated to ACTIVE when checkout completes
					subscriptionId: result.data.appSubscriptionCreate.appSubscription.id,
					confirmationUrl: result.confirmationUrl!,
					priceOverride: newPlanAmount
				});

				console.log(`Trial conversion initiated: created new subscription for plan ${currentActivePlan.id}`);

				return result;
			}
		} catch (error) {
			console.error(`Error during trial conversion for org ${orgId}:`, error);
			throw error;
		}
	}

	if (isReactivatingCurrentPlan) {
		console.log(`User is reactivating their current plan ${newPlanName}`);

		// For non-cancelled plans, clear any scheduled downgrades
		console.log(`Clearing any scheduled downgrades`);

		// First, find and log any PENDING_NEXT_RENEWAL plans for debugging
		const pendingPlans = await this.orgPlanRepository.find({
			where: {
				orgId: orgId,
				status: 'PENDING_NEXT_RENEWAL'
			}
		});
		console.log(`Found ${pendingPlans.length} PENDING_NEXT_RENEWAL plans for org ${orgId}:`, pendingPlans.map(p => ({ id: p.id, planId: p.planId, scheduledStart: p.scheduledStart })));

		// Cancel any PENDING_NEXT_RENEWAL plans specifically
		const cancelledCount = await this.orgPlanRepository.updateAll({
			status: 'CANCELLED',
			scheduledStart: undefined,
			scheduledEnd: undefined,
			subscriptionId: undefined,
			confirmationUrl: undefined
		}, {
			orgId: orgId,
			status: 'PENDING_NEXT_RENEWAL'
		});
		console.log(`Cancelled ${cancelledCount.count} PENDING_NEXT_RENEWAL plans for org ${orgId}`);

		// Then clear all scheduled dates for this organization (cancels any pending downgrades)
		const clearedCount = await this.orgPlanRepository.updateAll({
			scheduledEnd: undefined,
			scheduledStart: undefined
		}, {
			orgId: orgId
		});
		console.log(`Cleared scheduled dates for ${clearedCount.count} plans for org ${orgId}`);

		console.log(`Cleared all scheduled dates and cancelled pending plans for org ${orgId}`);


		// If the plan has a scheduledEnd (cancelled with cancel_at_period_end), reactivate it
		if (currentActivePlan.scheduledEnd && currentActivePlan.subscriptionId) {
			console.log(`Plan has scheduledEnd (${currentActivePlan.scheduledEnd}) - removing cancel_at_period_end`);

			try {
				// Remove cancel_at_period_end from Stripe subscription
				await this.reactivateSubscription(orgId, currentActivePlan.subscriptionId);

				// Clear the scheduled end date
				await this.orgPlanRepository.updateById(currentActivePlan.id, {
					scheduledEnd: undefined
				});

				console.log(`Successfully reactivated cancelled plan ${newPlanName} for org ${orgId}`);

				return {
					data: {
						subscriptionUpdate: {
							subscription: {
								id: currentActivePlan.subscriptionId,
								status: 'active',
							}
						}
					},
					confirmationUrl: '/loyalty/settings/plans'
				};
			} catch (error) {
				console.error(`Error reactivating cancelled plan:`, error);
				throw error;
			}
		}

		// Return success without changing anything else
		return {
			data: {
				subscriptionUpdate: {
					subscription: {
						id: currentActivePlan.subscriptionId,
						status: 'active',
					}
				}
			},
			confirmationUrl: '/loyalty/settings/plans'
		};
	}



	// 2. Find the specific plan we're interested in
	let orgPlan = await this.orgPlanRepository.findOne({
		where: {
			orgId: orgId,
			planId: newPlanId
		}
	});

	if (!orgPlan) {
		// Create new plan record - handle the typescript type issue
		const newOrgPlan = await this.orgPlanRepository.create({
			orgId: orgId,
			planId: newPlanId,
			priceOverride: newPlanAmount,
			status: 'PENDING', // Always set to PENDING until checkout is completed
			subscriptionId: undefined,
			confirmationUrl: undefined
		});
		console.log(`Created new org plan record: ${newOrgPlan.id}`);
		orgPlan = await this.orgPlanRepository.findById(newOrgPlan.id);
	} else {
		// Update the existing plan record to PENDING
		await this.orgPlanRepository.updateById(orgPlan.id, {
			status: 'PENDING',
			priceOverride: newPlanAmount
		});
		console.log(`Updated existing org plan record: ${orgPlan.id}`);
	}


	// For non-cancelled plans, clear any scheduled downgrades
	console.log(`Clearing any scheduled downgrades`);

	// First, find and log any PENDING_NEXT_RENEWAL plans for debugging
	const pendingPlans = await this.orgPlanRepository.find({
		where: {
			orgId: orgId,
			status: 'PENDING_NEXT_RENEWAL'
		}
	});
	console.log(`Found ${pendingPlans.length} PENDING_NEXT_RENEWAL plans for org ${orgId}:`, pendingPlans.map(p => ({ id: p.id, planId: p.planId, scheduledStart: p.scheduledStart })));

	// Cancel any PENDING_NEXT_RENEWAL plans specifically
	const cancelledCount = await this.orgPlanRepository.updateAll({
		status: 'CANCELLED',
		scheduledStart: undefined,
		scheduledEnd: undefined,
		subscriptionId: undefined,
		confirmationUrl: undefined
	}, {
		orgId: orgId,
		status: 'PENDING_NEXT_RENEWAL'
	});
	console.log(`Cancelled ${cancelledCount.count} PENDING_NEXT_RENEWAL plans for org ${orgId}`);

	// Then clear all scheduled dates for this organization (cancels any pending downgrades)
	const clearedCount = await this.orgPlanRepository.updateAll({
		scheduledEnd: undefined,
		scheduledStart: undefined
	}, {
		orgId: orgId
	});
	console.log(`Cleared scheduled dates for ${clearedCount.count} plans for org ${orgId}`);

	console.log(`Cleared all scheduled dates and cancelled pending plans for org ${orgId}`);


	const existingStripeSubscription = await this.getOrgSubscriptions(orgId);
	const activeStripeSubscriptions = existingStripeSubscription.filter(
		subscription => subscription.status === 'active' || subscription.status === 'trialing'
	);
	if (activeStripeSubscriptions.length > 1) {
		console.warn(`Multiple active subscriptions found for org ${orgId}, this may cause issues.`);
	}
	// If there are multiple active subscriptions, we need to handle this case
	if (activeStripeSubscriptions.length > 0) {
		console.log(`Found ${activeStripeSubscriptions.length} active subscriptions for org ${orgId}`);
	} else {
		console.log(`No active subscriptions found for org ${orgId}`);
	}

	const oldSubscriptionId = activeStripeSubscriptions.length > 0 ? activeStripeSubscriptions[0].id : undefined;

    try {
      // Adjust price based on revenue if needed
      let finalAmount = newPlanAmount;

      // Find or create product for the plan
      const product = await this.findOrCreateProduct(newPlanName);

      // Find or create a price for the plan
      const price = await this.findOrCreatePrice(
        finalAmount,
        product.id,
        this.convertIntervalToStripe(newPlanInterval)
      );

      // If there's an existing subscription, update it
      if (oldSubscriptionId) {
        try {
          // First check if the subscription is valid
          const existingSubscription = await this.stripe.subscriptions.retrieve(oldSubscriptionId);

          // Only update if the subscription is active or trialing
          if (existingSubscription.status === 'active' || existingSubscription.status === 'trialing') {

            if (isDowngradeChange) {
              // For downgrades: Update Stripe immediately but schedule internal transition
              console.log(`Processing downgrade for org ${orgId}: updating Stripe immediately but scheduling internal transition`);

              // Get the original billing period end date before updating Stripe
              const originalBillingEndDate = await this.getOriginalBillingEndDate(oldSubscriptionId);

              // Update the subscription in Stripe immediately (like upgrades)
              const result = await this.updateSubscription(orgId, oldSubscriptionId, price.id);

              if (originalBillingEndDate) {
                // Mark current plan to end at original billing cycle end
                const currentOrgPlan = await this.orgPlanRepository.findOne({
                  where: { orgId: orgId, status: 'ACTIVE' }
                });

              	// Clear any existing scheduled dates for this organization (cancels any pending downgrades)
				await this.orgPlanRepository.updateAll({
					scheduledEnd: undefined,
					scheduledStart: undefined
				}, {
					orgId: orgId
				});

                if (currentOrgPlan) {
                  await this.orgPlanRepository.updateById(currentOrgPlan.id, {
                    scheduledEnd: originalBillingEndDate.toISOString()
                  });
                  console.log(`Current plan ${currentOrgPlan.id} scheduled to end at original billing date: ${originalBillingEndDate.toISOString()}`);
                }


                // Cancel any existing PENDING_NEXT_RENEWAL plans and clear their scheduled dates
                console.log(`Cancelling any existing plans for org ${orgId} before setting new downgrade`);
                await this.orgPlanRepository.updateAll({
                  status: 'CANCELLED',
                  scheduledStart: undefined,
                  scheduledEnd: undefined,
                  subscriptionId: undefined,
                  confirmationUrl: undefined
                }, {
                  orgId: orgId,
                  status: { neq: 'ACTIVE' },
                });

                // Mark new plan as pending next renewal
                await this.orgPlanRepository.updateById(orgPlan.id, {
                  status: 'PENDING_NEXT_RENEWAL',
                  scheduledStart: originalBillingEndDate.toISOString(),
                  subscriptionId: existingSubscription.id
                });

                console.log(`Scheduled downgrade for org ${orgId}: current plan ends ${originalBillingEndDate.toISOString()}, new plan starts ${originalBillingEndDate.toISOString()}`);
              } else {
                console.warn(`Could not determine original billing end date for downgrade, applying immediately`);

                // Fallback to immediate transition
                await this.orgPlanRepository.updateById(orgPlan.id, {
                  status: 'ACTIVE',
                  subscriptionId: existingSubscription.id,
                  confirmationUrl: result.confirmationUrl!
                });

                // Cancel all other plans and clear scheduled dates
                await this.orgPlanRepository.updateAll({
                  status: 'CANCELLED',
                  subscriptionId: undefined,
                  confirmationUrl: undefined,
                  scheduledEnd: undefined,
                  scheduledStart: undefined
                }, {
                  orgId: orgId,
                  id: { nin: [orgPlan.id] }
                });
              }

              return {
                data: {
                  subscriptionUpdate: {
                    subscription: {
                      id: existingSubscription.id,
                      status: existingSubscription.status,
                    }
                  }
                },
                confirmationUrl: '/loyalty/settings/plans'
              };
            } else {
              // For upgrades: Apply immediately with proration
              const result = await this.updateSubscription(orgId, oldSubscriptionId, price.id);

              // Clear any existing scheduled dates for this organization (cancels any pending downgrades)
              await this.orgPlanRepository.updateAll({
                scheduledEnd: undefined,
                scheduledStart: undefined
              }, {
                orgId: orgId
              });

              // Update orgPlan with subscriptionId and confirmationUrl
              await this.orgPlanRepository.updateById(orgPlan.id, {
                status: 'ACTIVE',
                subscriptionId: existingSubscription.id,
                confirmationUrl: result.confirmationUrl!,
                scheduledEnd: undefined,
                scheduledStart: undefined
              });

              // update all other plans to CANCELLED and clear their scheduled dates
              await this.orgPlanRepository.updateAll({
                status: 'CANCELLED',
                subscriptionId: undefined,
                confirmationUrl: undefined,
                scheduledEnd: undefined,
                scheduledStart: undefined
              }, {
                orgId: orgId,
                id: {
                  nin: [orgPlan.id]
                }
              });

              console.log(`Updated existing subscription ${oldSubscriptionId} to new plan ${newPlanName} and cleared all scheduled dates`);

              return result;
            }
          } else {
            console.log(`Existing subscription ${oldSubscriptionId} has status ${existingSubscription.status}, creating new one`);
          }
        } catch (err) {
          console.warn(`Could not retrieve existing subscription ${oldSubscriptionId}, creating new one:`, err);
          // Continue to create a new subscription
        }
      }


      // Create a new subscription
      const result = await this.createSubscription(
        orgId,
        finalAmount,
        newPlanName,
        newPlanInterval,
        'https://app.raleon.io/loyalty/settings/plans',
        false
      );

      // Clear any existing scheduled dates for this organization (cancels any pending downgrades)
      await this.orgPlanRepository.updateAll({
        scheduledEnd: undefined,
        scheduledStart: undefined
      }, {
        orgId: orgId
      });

	  // update orgPlan with subscriptionId and confirmationUrl
	  await this.orgPlanRepository.updateById(orgPlan.id, {
		status: 'PENDING',
		subscriptionId: result.data.appSubscriptionCreate.appSubscription.id,
		confirmationUrl: result.confirmationUrl!,
		scheduledEnd: undefined,
		scheduledStart: undefined
	  });

	  return result;
    } catch (error) {
      console.error(`Error migrating org ${orgId} to plan ${newPlanName}:`, error);
      throw error;
    }
  }


  async cancelLegacyShopifySubscriptions(orgId: number) {

		const allOrgPlans = await this.orgPlanRepository.find({
			where: {
				orgId: orgId,
			}
		});

        // Check for existing Shopify subscription through the organization plans
        const shopifySubscriptionPlans = allOrgPlans.filter(
            plan => plan.status === 'ACTIVE' &&
                    plan.subscriptionId &&
                    plan.subscriptionId.startsWith('gid://shopify')
        );

        // Cancel any active Shopify subscriptions if they exist
        if (shopifySubscriptionPlans.length > 0) {
            console.log(`Found ${shopifySubscriptionPlans.length} active Shopify subscriptions for org ${orgId}`);

            for (const shopifyPlan of shopifySubscriptionPlans) {
                try {
                    console.log(`Cancelling Shopify subscription ${shopifyPlan.subscriptionId} before plan change`);
                    await this.billingService.unsubscribe(orgId, shopifyPlan.subscriptionId!);

                    // Update the plan status to cancelled and clear scheduled dates
                    await this.orgPlanRepository.updateById(shopifyPlan.id, {
                        status: 'CANCELLED',
                        subscriptionId: undefined,
                        confirmationUrl: undefined,
                        scheduledEnd: undefined,
                        scheduledStart: undefined
                    });

                    console.log(`Successfully cancelled Shopify subscription for plan ID: ${shopifyPlan.planId}`);
                } catch (err) {
                    console.error(`Failed to cancel Shopify subscription ${shopifyPlan.subscriptionId}:`, err);
                }
            }
        }
	}

  /**
   * Find or create a product in Stripe
   */
  private async findOrCreateProduct(name: string) {
    try {
      // Try to find the product first
      const products = await this.stripe.products.list({
        limit: 10,
        active: true,
      });

      const existingProduct = products.data.find(
        product => product.name.toLowerCase() === name.toLowerCase()
      );

      if (existingProduct) {
        console.log(`Found existing product for ${name}: ${existingProduct.id}`);
        return existingProduct;
      }

      // If product doesn't exist, create one
      const product = await this.stripe.products.create({
        name: name,
        description: `${name} Plan for Raleon`,
        metadata: {
          planName: name,
          createdAt: new Date().toISOString(),
        },
      });

      console.log(`Created new product for ${name}: ${product.id}`);
      return product;
    } catch (error) {
      console.error(`Error finding/creating product for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Find or create a price in Stripe
   */
  private async findOrCreatePrice(amount: number, productId: string, interval: string) {
    try {
      // Create a lookup key from the product ID, amount, and interval
      const lookupKey = `${productId}_${amount}_${interval}`;

      // Try to find the price first
      const prices = await this.stripe.prices.list({
        limit: 10,
        product: productId,
        active: true,
      });

      // Find a price that matches our criteria
      const existingPrice = prices.data.find(
        price =>
          price.unit_amount === amount * 100 &&
          price.recurring?.interval === interval
      );

      if (existingPrice) {
        console.log(`Found existing price ${existingPrice.id} for product ${productId} at $${amount}/${interval}`);
        return existingPrice;
      }

      // If price doesn't exist, create one
      const price = await this.stripe.prices.create({
        unit_amount: amount * 100, // Convert to cents
        currency: 'usd',
        recurring: { interval: interval as Stripe.PriceCreateParams.Recurring.Interval },
        product: productId,
        lookup_key: lookupKey,
        metadata: {
          amountUsd: amount.toString(),
          interval,
          createdAt: new Date().toISOString(),
        },
      });

      console.log(`Created new price ${price.id} for product ${productId} at $${amount}/${interval}`);
      return price;
    } catch (error) {
      console.error(`Error finding/creating price for product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Convert interval format from app format to Stripe format
   */
  private convertIntervalToStripe(interval: string): string {
    // Convert from Shopify format to Stripe format
    switch (interval) {
      case 'EVERY_30_DAYS':
        return 'month';
      case 'ANNUAL':
        return 'year';
      case 'EVERY_7_DAYS':
        return 'week';
      case 'QUARTERLY':
      case 'EVERY_90_DAYS':
        return 'month'; // Stripe doesn't have quarterly, use month with 3 count
      default:
        return 'month'; // Default to monthly billing
    }
  }

//   /**
//    * Retrieve subscription details from Stripe
//    */
//   private async getSubscription(subscriptionId: string) {
//     try {
//       const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
//       return subscription;
//     } catch (error) {
//       console.error(`Error retrieving subscription ${subscriptionId}:`, error);
//       throw error;
//     }
//   }

//   /**
//    * List all subscriptions for a customer
//    */
//   private async listCustomerSubscriptions(customerId: string) {
//     try {
//       const subscriptions = await this.stripe.subscriptions.list({
//         customer: customerId,
//         status: 'all',
//         limit: 100,
//       });
//       return subscriptions;
//     } catch (error) {
//       console.error(`Error listing subscriptions for customer ${customerId}:`, error);
//       throw error;
//     }
//   }

  /**
   * Clean up orphaned or stale subscriptions for an organization
   * This should be called after migrations between plans or when troubleshooting issues
   */
  async cleanupSubscriptions(orgId: number, subscriptionIdToKeep?: string): Promise<string[]> {
    console.log(`Cleaning up subscriptions for org ${orgId}`);

    try {
      // First, find the customer for this organization
      const customers = await this.stripe.customers.list({
        limit: 100,
      });

      const customer = customers.data.find(
        c => c.metadata && c.metadata.orgId === orgId.toString()
      );

      if (!customer) {
        console.log(`No Stripe customer found for org ${orgId}`);
        return [];
      }

      // Get all subscriptions for this customer
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customer.id,
        limit: 100,
      });

      const cancelledSubscriptionIds: string[] = [];

      // Cancel any active subscriptions that are not the current active one
      for (const subscription of subscriptions.data) {
        // Skip already cancelled subscriptions
        if (subscription.status === 'canceled' || subscription.status === 'incomplete_expired') {
          continue;
        }

		// Skip the subscription we want to keep
		if (subscriptionIdToKeep && subscription.id === subscriptionIdToKeep) {
			console.log(`Skipping subscription ${subscription.id} as it is the one to keep`);
			continue;
		}

        // // For subscriptions in a problematic state, cancel them
        // if (
        //   subscription.status === 'past_due' ||
        //   subscription.status === 'unpaid' ||
        //   subscription.status === 'incomplete'
        // ) {
        //   console.log(`Canceling problematic subscription ${subscription.id} with status ${subscription.status}`);

          await this.stripe.subscriptions.cancel(subscription.id, {
            prorate: false,
          });

          cancelledSubscriptionIds.push(subscription.id);
        // }
      }

      return cancelledSubscriptionIds;
    } catch (error) {
      console.error(`Error cleaning up subscriptions for org ${orgId}:`, error);
      return [];
    }
  }

  /**
   * Get subscriptions for a specific organization
   */
  private async getOrgSubscriptions(orgId: number): Promise<Stripe.Subscription[]> {
    console.log(`Getting subscriptions for org ${orgId}`);

    try {
      // First, find the customer for this organization
      const customers = await this.stripe.customers.list({
        limit: 100,
      });

      const customer = customers.data.find(
        c => c.metadata && c.metadata.orgId === orgId.toString()
      );

      if (!customer) {
        console.log(`No Stripe customer found for org ${orgId}`);
        return [];
      }

      // Get all subscriptions for this customer
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customer.id,
        limit: 100,
      });

      return subscriptions.data;
    } catch (error) {
      console.error(`Error getting subscriptions for org ${orgId}:`, error);
      return [];
    }
  }
}
