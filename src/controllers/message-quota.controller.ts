import {inject} from '@loopback/core';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {post, get, requestBody, api} from '@loopback/rest';
import {MessageQuotaService} from '../services/message-quota.service';
import {MessageCreditRepository, OrganizationPlanRepository} from '../repositories';
import {MessageCredit} from '../models';
import {repository} from '@loopback/repository';
import {GuardSkipStrategy, guardStrategy, injectUserOrgId, skipGuardCheck} from '../interceptors';
import {basicAuthorization} from '../services';

@authenticate('jwt')
@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class MessageQuotaController {
	constructor(
		@inject('services.MessageQuotaService') private quotaService: MessageQuotaService,
		@repository(MessageCreditRepository) private creditRepo: MessageCreditRepository,
		@repository(OrganizationPlanRepository) private orgPlanRepo: OrganizationPlanRepository,
	) { }

	@get('/messages/quota')
	@skipGuardCheck()
	@authorize({allowedRoles: ['admin', 'support', 'customer'], voters: [basicAuthorization]})
	async getQuota(@injectUserOrgId() orgId: number) {
		const quotaStatus = await this.quotaService.getQuotaStatus(orgId);
		
		// Get non-daily rewards that have been claimed
		const claimedRewards = await this.creditRepo.find({
			where: {
				organizationId: orgId,
				type: {nin: ['daily', 'dailyLimit', 'dailyLogin']} // Exclude daily items
			},
			fields: ['type', 'credits', 'createdAt']
		});

		// Check if daily login was claimed today
		const today = new Date();
		today.setUTCHours(0, 0, 0, 0);
		
		const dailyLoginToday = await this.creditRepo.findOne({
			where: {
				organizationId: orgId,
				type: 'dailyLogin',
				createdAt: {gte: today.toISOString()}
			}
		});

		const allClaimedRewards = claimedRewards.map(r => r.type);
		if (dailyLoginToday) {
			allClaimedRewards.push('dailyLogin');
		}

		return {
			...quotaStatus,
			claimedRewards: allClaimedRewards
		};
	}

	@post('/messages/quota/earn')
	@skipGuardCheck()
	@authorize({allowedRoles: ['admin', 'support', 'customer'], voters: [basicAuthorization]})
	async earnPremiumCredits(
		@requestBody() body: {type: string, amount: number, reason?: string},
		@injectUserOrgId() orgId: number,
	) {
		// Special handling for daily login - check if claimed today
		if (body.type === 'dailyLogin') {
			const today = new Date();
			today.setUTCHours(0, 0, 0, 0);
			
			const todayClaimCheck = await this.creditRepo.findOne({
				where: {
					organizationId: orgId,
					type: 'dailyLogin',
					createdAt: {gte: today.toISOString()}
				}
			});

			if (todayClaimCheck) {
				return {
					success: false,
					message: 'Daily login bonus already claimed today',
					alreadyClaimed: true
				};
			}
		} else {
			// Check if this reward type has already been claimed for this organization
			const existingReward = await this.creditRepo.findOne({
				where: {
					organizationId: orgId,
					type: body.type
				}
			});

			if (existingReward) {
				return {
					success: false,
					message: `Reward of type '${body.type}' has already been claimed`,
					alreadyClaimed: true
				};
			}
		}

		// Premium credits are always additive, never replace existing ones
		const credit = await this.creditRepo.create({
			organizationId: orgId,
			type: body.type, // e.g., 'shopify', 'klaviyo', 'referral', etc.
			credits: body.amount,
		} as MessageCredit);

		return {
			success: true,
			message: `Awarded ${body.amount} premium messages for ${body.type}`,
			creditId: credit.id,
			reason: body.reason
		};
	}

	@post('/messages/quota/reset-daily')
	@authenticate('api-key')
	@skipGuardCheck()
	async resetDailyQuotas() {
		try {
			// Get all active organization plans
			const activePlans = await this.orgPlanRepo.find({where: {status: 'ACTIVE'}});
			const results = [];

			for (const plan of activePlans) {
				// Just track the daily limits, no credits needed
				// The daily limits are enforced by checking usage vs plan limits
				let dailyLimit = 5; // default free plan
				if (plan.planId === 0 || plan.planId === 1) {
					dailyLimit = 5; // free plans
				} else {
					dailyLimit = 100; // paid plans
				}

				results.push({
					orgId: plan.orgId,
					planId: plan.planId,
					dailyLimit: dailyLimit,
					resetAt: new Date()
				});
			}

			return {
				success: true,
				message: `Daily quotas reset for ${results.length} organizations - limits are enforced based on plan`,
				results
			};
		} catch (error) {
			return {
				success: false,
				message: 'Failed to reset daily quotas',
				error: error.message
			};
		}
	}
}
